# Environment Configuration for Scribe Pro

# Application
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# Database
DATABASE_URL=postgresql://scribe_user:scribe_password@localhost:5432/scribe_pro

# Redis Cache
REDIS_URL=redis://localhost:6379

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# AWS S3 / MinIO Storage
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin123
AWS_ENDPOINT=http://localhost:9000
AWS_BUCKET_NAME=scribe-pro-uploads
AWS_REGION=us-east-1
AWS_FORCE_PATH_STYLE=true

# Email (for notifications and invites)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Scribe Pro

# Analytics (optional)
GOOGLE_ANALYTICS_ID=
MIXPANEL_TOKEN=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE=10485760
MAX_FILES_PER_TUTORIAL=50

# WebSocket
WS_HEARTBEAT_INTERVAL=30000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security
CORS_ORIGIN=http://localhost:5173
HELMET_ENABLED=true
TRUST_PROXY=false

# Feature Flags
ENABLE_COLLABORATION=true
ENABLE_ANALYTICS=true
ENABLE_WEBHOOKS=false
ENABLE_API_DOCS=true

# Third-party Integrations
NOTION_CLIENT_ID=
NOTION_CLIENT_SECRET=
SLACK_CLIENT_ID=
SLACK_CLIENT_SECRET=
ZAPIER_WEBHOOK_URL=
