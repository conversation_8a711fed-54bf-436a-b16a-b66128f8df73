import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '@/utils/logger';
import { config } from '@/config';
import { DatabaseService } from './database';
import { RedisService } from './redis';
import type { WebSocketMessage, CollaborationEvent, User } from '@/types';

interface AuthenticatedSocket extends Socket {
  user?: User;
  sessionId?: string;
}

export class WebSocketService {
  private io: SocketIOServer;
  private databaseService: DatabaseService;
  private redisService: RedisService;
  private connectedUsers: Map<string, Set<string>> = new Map(); // userId -> Set of socketIds
  private tutorialRooms: Map<string, Set<string>> = new Map(); // tutorialId -> Set of userIds

  constructor(io: SocketIOServer) {
    this.io = io;
    this.databaseService = new DatabaseService();
    this.redisService = new RedisService();
  }

  initialize(): void {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, config.jwt.secret) as any;
        const user = await this.databaseService.getUserById(decoded.userId);
        
        if (!user || !user.isActive) {
          return next(new Error('User not found or inactive'));
        }

        socket.user = user;
        socket.sessionId = decoded.sessionId;
        next();
      } catch (error) {
        logger.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Connection handling
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      this.handleConnection(socket);
    });

    logger.info('WebSocket service initialized');
  }

  private handleConnection(socket: AuthenticatedSocket): void {
    const user = socket.user!;
    const userId = user.id;

    logger.info('User connected via WebSocket', {
      userId,
      socketId: socket.id,
      email: user.email,
    });

    // Track connected user
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }
    this.connectedUsers.get(userId)!.add(socket.id);

    // Join user to their personal room
    socket.join(`user:${userId}`);

    // Handle tutorial collaboration
    socket.on('join_tutorial', async (data: { tutorialId: string }) => {
      await this.handleJoinTutorial(socket, data.tutorialId);
    });

    socket.on('leave_tutorial', async (data: { tutorialId: string }) => {
      await this.handleLeaveTutorial(socket, data.tutorialId);
    });

    // Handle real-time collaboration events
    socket.on('tutorial_update', async (data: any) => {
      await this.handleTutorialUpdate(socket, data);
    });

    socket.on('step_update', async (data: any) => {
      await this.handleStepUpdate(socket, data);
    });

    socket.on('step_create', async (data: any) => {
      await this.handleStepCreate(socket, data);
    });

    socket.on('step_delete', async (data: any) => {
      await this.handleStepDelete(socket, data);
    });

    socket.on('cursor_move', async (data: any) => {
      await this.handleCursorMove(socket, data);
    });

    socket.on('typing_start', async (data: any) => {
      await this.handleTypingStart(socket, data);
    });

    socket.on('typing_stop', async (data: any) => {
      await this.handleTypingStop(socket, data);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });

    // Send welcome message
    socket.emit('connected', {
      message: 'Connected to Scribe Pro',
      userId,
      timestamp: new Date().toISOString(),
    });
  }

  private async handleJoinTutorial(socket: AuthenticatedSocket, tutorialId: string): Promise<void> {
    const user = socket.user!;
    const userId = user.id;

    try {
      // Check if user has access to tutorial
      const tutorial = await this.databaseService.getTutorialById(tutorialId);
      if (!tutorial) {
        socket.emit('error', { message: 'Tutorial not found' });
        return;
      }

      // Check permissions
      const hasAccess = tutorial.isPublic || 
                       tutorial.authorId === userId ||
                       (tutorial.teamId && await this.databaseService.getTeamMembership(userId, tutorial.teamId));

      if (!hasAccess) {
        socket.emit('error', { message: 'Access denied' });
        return;
      }

      // Join tutorial room
      socket.join(`tutorial:${tutorialId}`);

      // Track user in tutorial
      if (!this.tutorialRooms.has(tutorialId)) {
        this.tutorialRooms.set(tutorialId, new Set());
      }
      this.tutorialRooms.get(tutorialId)!.add(userId);

      // Notify other users in the tutorial
      socket.to(`tutorial:${tutorialId}`).emit('user_joined', {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
        },
        tutorialId,
        timestamp: new Date().toISOString(),
      });

      // Send current collaborators to the joining user
      const collaborators = Array.from(this.tutorialRooms.get(tutorialId)!)
        .filter(id => id !== userId)
        .map(id => ({ userId: id })); // In a real app, you'd fetch user details

      socket.emit('tutorial_joined', {
        tutorialId,
        collaborators,
        timestamp: new Date().toISOString(),
      });

      logger.info('User joined tutorial', { userId, tutorialId });

    } catch (error) {
      logger.error('Error joining tutorial:', error);
      socket.emit('error', { message: 'Failed to join tutorial' });
    }
  }

  private async handleLeaveTutorial(socket: AuthenticatedSocket, tutorialId: string): Promise<void> {
    const user = socket.user!;
    const userId = user.id;

    socket.leave(`tutorial:${tutorialId}`);

    // Remove user from tutorial tracking
    if (this.tutorialRooms.has(tutorialId)) {
      this.tutorialRooms.get(tutorialId)!.delete(userId);
      
      // Clean up empty rooms
      if (this.tutorialRooms.get(tutorialId)!.size === 0) {
        this.tutorialRooms.delete(tutorialId);
      }
    }

    // Notify other users
    socket.to(`tutorial:${tutorialId}`).emit('user_left', {
      userId,
      tutorialId,
      timestamp: new Date().toISOString(),
    });

    logger.info('User left tutorial', { userId, tutorialId });
  }

  private async handleTutorialUpdate(socket: AuthenticatedSocket, data: any): Promise<void> {
    const { tutorialId, changes } = data;
    const user = socket.user!;

    // Broadcast to other users in the tutorial
    socket.to(`tutorial:${tutorialId}`).emit('tutorial_updated', {
      tutorialId,
      changes,
      updatedBy: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      timestamp: new Date().toISOString(),
    });

    // Cache the update for conflict resolution
    await this.redisService.setJSON(
      `tutorial_update:${tutorialId}:${Date.now()}`,
      { changes, updatedBy: user.id },
      300 // 5 minutes TTL
    );
  }

  private async handleStepUpdate(socket: AuthenticatedSocket, data: any): Promise<void> {
    const { tutorialId, stepId, changes } = data;
    const user = socket.user!;

    socket.to(`tutorial:${tutorialId}`).emit('step_updated', {
      tutorialId,
      stepId,
      changes,
      updatedBy: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      timestamp: new Date().toISOString(),
    });
  }

  private async handleStepCreate(socket: AuthenticatedSocket, data: any): Promise<void> {
    const { tutorialId, step } = data;
    const user = socket.user!;

    socket.to(`tutorial:${tutorialId}`).emit('step_created', {
      tutorialId,
      step,
      createdBy: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      timestamp: new Date().toISOString(),
    });
  }

  private async handleStepDelete(socket: AuthenticatedSocket, data: any): Promise<void> {
    const { tutorialId, stepId } = data;
    const user = socket.user!;

    socket.to(`tutorial:${tutorialId}`).emit('step_deleted', {
      tutorialId,
      stepId,
      deletedBy: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      timestamp: new Date().toISOString(),
    });
  }

  private async handleCursorMove(socket: AuthenticatedSocket, data: any): Promise<void> {
    const { tutorialId, position } = data;
    const user = socket.user!;

    socket.to(`tutorial:${tutorialId}`).emit('cursor_moved', {
      userId: user.id,
      position,
      timestamp: new Date().toISOString(),
    });
  }

  private async handleTypingStart(socket: AuthenticatedSocket, data: any): Promise<void> {
    const { tutorialId, stepId, field } = data;
    const user = socket.user!;

    socket.to(`tutorial:${tutorialId}`).emit('typing_started', {
      userId: user.id,
      stepId,
      field,
      timestamp: new Date().toISOString(),
    });
  }

  private async handleTypingStop(socket: AuthenticatedSocket, data: any): Promise<void> {
    const { tutorialId, stepId, field } = data;
    const user = socket.user!;

    socket.to(`tutorial:${tutorialId}`).emit('typing_stopped', {
      userId: user.id,
      stepId,
      field,
      timestamp: new Date().toISOString(),
    });
  }

  private handleDisconnection(socket: AuthenticatedSocket): void {
    const user = socket.user;
    if (!user) return;

    const userId = user.id;

    logger.info('User disconnected from WebSocket', {
      userId,
      socketId: socket.id,
      email: user.email,
    });

    // Remove socket from user tracking
    if (this.connectedUsers.has(userId)) {
      this.connectedUsers.get(userId)!.delete(socket.id);
      
      // If user has no more connections, remove from all tutorials
      if (this.connectedUsers.get(userId)!.size === 0) {
        this.connectedUsers.delete(userId);
        
        // Remove from all tutorial rooms
        for (const [tutorialId, users] of this.tutorialRooms.entries()) {
          if (users.has(userId)) {
            users.delete(userId);
            
            // Notify other users
            socket.to(`tutorial:${tutorialId}`).emit('user_left', {
              userId,
              tutorialId,
              timestamp: new Date().toISOString(),
            });
            
            // Clean up empty rooms
            if (users.size === 0) {
              this.tutorialRooms.delete(tutorialId);
            }
          }
        }
      }
    }
  }

  // Public methods for sending messages
  public sendToUser(userId: string, event: string, data: any): void {
    this.io.to(`user:${userId}`).emit(event, data);
  }

  public sendToTutorial(tutorialId: string, event: string, data: any): void {
    this.io.to(`tutorial:${tutorialId}`).emit(event, data);
  }

  public broadcastToAll(event: string, data: any): void {
    this.io.emit(event, data);
  }

  public getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }

  public getTutorialCollaborators(tutorialId: string): string[] {
    return Array.from(this.tutorialRooms.get(tutorialId) || []);
  }
}
