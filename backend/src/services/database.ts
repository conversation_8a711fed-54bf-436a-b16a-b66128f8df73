import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';
import { config } from '@/config';
import type { 
  User, 
  Tutorial, 
  Step, 
  Team, 
  TeamMember, 
  Session, 
  ApiKey,
  CreateTutorialRequest,
  UpdateTutorialRequest,
  CreateStepRequest,
  UpdateStepRequest,
  TutorialQuery,
  PaginationQuery
} from '@/types';

export class DatabaseService {
  private prisma: PrismaClient;
  private isConnected: boolean = false;

  constructor() {
    this.prisma = new PrismaClient({
      log: config.app.isDevelopment ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });
  }

  async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      this.isConnected = true;
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      this.isConnected = false;
      logger.info('Database disconnected');
    } catch (error) {
      logger.error('Failed to disconnect from database:', error);
      throw error;
    }
  }

  // User methods
  async createUser(userData: {
    email: string;
    passwordHash: string;
    firstName?: string;
    lastName?: string;
    username?: string;
  }): Promise<User> {
    return this.prisma.user.create({
      data: userData,
    });
  }

  async getUserById(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id },
    });
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data,
    });
  }

  async deleteUser(id: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id },
    });
  }

  // Tutorial methods
  async createTutorial(authorId: string, data: CreateTutorialRequest): Promise<Tutorial> {
    return this.prisma.tutorial.create({
      data: {
        ...data,
        authorId,
      },
      include: {
        steps: {
          orderBy: { order: 'asc' },
        },
        author: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    });
  }

  async getTutorialById(id: string): Promise<Tutorial | null> {
    return this.prisma.tutorial.findUnique({
      where: { id },
      include: {
        steps: {
          orderBy: { order: 'asc' },
        },
        author: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        team: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });
  }

  async getTutorials(query: TutorialQuery): Promise<{
    tutorials: Tutorial[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 10,
      search,
      tags,
      category,
      isPublic,
      authorId,
      teamId,
      sortBy = 'updatedAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (tags && tags.length > 0) {
      where.tags = { hasSome: tags };
    }

    if (category) {
      where.category = category;
    }

    if (typeof isPublic === 'boolean') {
      where.isPublic = isPublic;
    }

    if (authorId) {
      where.authorId = authorId;
    }

    if (teamId) {
      where.teamId = teamId;
    }

    const [tutorials, total] = await Promise.all([
      this.prisma.tutorial.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          steps: {
            orderBy: { order: 'asc' },
            take: 1, // Only get first step for preview
          },
          author: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
          team: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      }),
      this.prisma.tutorial.count({ where }),
    ]);

    return {
      tutorials,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateTutorial(id: string, data: UpdateTutorialRequest): Promise<Tutorial> {
    return this.prisma.tutorial.update({
      where: { id },
      data,
      include: {
        steps: {
          orderBy: { order: 'asc' },
        },
        author: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    });
  }

  async deleteTutorial(id: string): Promise<void> {
    await this.prisma.tutorial.delete({
      where: { id },
    });
  }

  async incrementTutorialViews(id: string): Promise<void> {
    await this.prisma.tutorial.update({
      where: { id },
      data: {
        viewCount: {
          increment: 1,
        },
      },
    });
  }

  // Step methods
  async createStep(tutorialId: string, data: CreateStepRequest): Promise<Step> {
    // Get the next order number
    const lastStep = await this.prisma.step.findFirst({
      where: { tutorialId },
      orderBy: { order: 'desc' },
    });

    const order = lastStep ? lastStep.order + 1 : 1;

    return this.prisma.step.create({
      data: {
        ...data,
        tutorialId,
        order,
      },
    });
  }

  async getStepById(id: string): Promise<Step | null> {
    return this.prisma.step.findUnique({
      where: { id },
    });
  }

  async getStepsByTutorialId(tutorialId: string): Promise<Step[]> {
    return this.prisma.step.findMany({
      where: { tutorialId },
      orderBy: { order: 'asc' },
    });
  }

  async updateStep(id: string, data: UpdateStepRequest): Promise<Step> {
    return this.prisma.step.update({
      where: { id },
      data,
    });
  }

  async deleteStep(id: string): Promise<void> {
    await this.prisma.step.delete({
      where: { id },
    });
  }

  async reorderSteps(tutorialId: string, stepOrders: { id: string; order: number }[]): Promise<void> {
    await this.prisma.$transaction(
      stepOrders.map(({ id, order }) =>
        this.prisma.step.update({
          where: { id },
          data: { order },
        })
      )
    );
  }

  // Session methods
  async createSession(userId: string, token: string, expiresAt: Date): Promise<Session> {
    return this.prisma.session.create({
      data: {
        userId,
        token,
        expiresAt,
      },
    });
  }

  async getSessionById(id: string): Promise<Session | null> {
    return this.prisma.session.findUnique({
      where: { id },
    });
  }

  async getSessionByToken(token: string): Promise<Session | null> {
    return this.prisma.session.findUnique({
      where: { token },
    });
  }

  async updateSessionActivity(id: string): Promise<void> {
    await this.prisma.session.update({
      where: { id },
      data: {
        updatedAt: new Date(),
      },
    });
  }

  async deleteSession(id: string): Promise<void> {
    await this.prisma.session.delete({
      where: { id },
    });
  }

  async deleteExpiredSessions(): Promise<void> {
    await this.prisma.session.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });
  }

  // Team methods
  async getTeamMembership(userId: string, teamId: string): Promise<TeamMember | null> {
    return this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          userId,
          teamId,
        },
      },
    });
  }

  // API Key methods
  async getApiKey(key: string): Promise<ApiKey | null> {
    return this.prisma.apiKey.findUnique({
      where: { key },
    });
  }

  async updateApiKeyUsage(id: string): Promise<void> {
    await this.prisma.apiKey.update({
      where: { id },
      data: {
        lastUsedAt: new Date(),
      },
    });
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }
}
