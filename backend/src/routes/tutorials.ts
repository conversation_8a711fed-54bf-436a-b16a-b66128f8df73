import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { DatabaseService } from '@/services/database';
import { RedisService } from '@/services/redis';
import { WebSocketService } from '@/services/websocket';
import { logger } from '@/utils/logger';
import { as<PERSON><PERSON><PERSON><PERSON> } from '@/middleware/error';
import { validateRequest, validateQuery, validateParams, commonSchemas } from '@/middleware/validation';
import { requireOwnership, optionalAuthMiddleware } from '@/middleware/auth';
import type { CreateTutorialRequest, UpdateTutorialRequest, TutorialQuery } from '@/types';
import { NotFoundError, AuthorizationError } from '@/types';

const router = Router();
const databaseService = new DatabaseService();
const redisService = new RedisService();

// Validation schemas
const createTutorialSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  tags: z.array(z.string()).max(10, 'Maximum 10 tags allowed').optional(),
  category: z.string().max(50, 'Category must be less than 50 characters').optional(),
  isPublic: z.boolean().optional(),
  teamId: z.string().cuid('Invalid team ID').optional(),
});

const updateTutorialSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters').optional(),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  tags: z.array(z.string()).max(10, 'Maximum 10 tags allowed').optional(),
  category: z.string().max(50, 'Category must be less than 50 characters').optional(),
  isPublic: z.boolean().optional(),
  settings: z.record(z.any()).optional(),
});

const tutorialQuerySchema = commonSchemas.paginationQuery.merge(commonSchemas.searchQuery);

const shareSettingsSchema = z.object({
  allowComments: z.boolean().optional(),
  allowDownload: z.boolean().optional(),
  expiresAt: z.string().datetime().optional(),
  password: z.string().optional(),
});

// Routes

// GET /api/tutorials - Get all tutorials
router.get('/', 
  validateQuery(tutorialQuerySchema),
  asyncHandler(async (req: any, res: Response) => {
    const query: TutorialQuery = {
      ...req.query,
      authorId: req.user?.id, // Only show user's tutorials if authenticated
    };

    // Cache key for tutorials list
    const cacheKey = `tutorials:${req.user?.id || 'public'}:${JSON.stringify(query)}`;
    
    const result = await redisService.cache(
      cacheKey,
      () => databaseService.getTutorials(query),
      300 // 5 minutes cache
    );

    if (!result) {
      throw new Error('Failed to fetch tutorials');
    }

    logger.info('Tutorials fetched', {
      userId: req.user?.id,
      count: result.tutorials.length,
      total: result.total,
    });

    res.json({
      success: true,
      data: result.tutorials,
      pagination: {
        page: result.page,
        limit: query.limit || 10,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  })
);

// GET /api/tutorials/public - Get public tutorials
router.get('/public',
  optionalAuthMiddleware,
  validateQuery(tutorialQuerySchema),
  asyncHandler(async (req: Request, res: Response) => {
    const query: TutorialQuery = {
      ...req.query,
      isPublic: true,
    };

    const cacheKey = `tutorials:public:${JSON.stringify(query)}`;
    
    const result = await redisService.cache(
      cacheKey,
      () => databaseService.getTutorials(query),
      600 // 10 minutes cache for public tutorials
    );

    if (!result) {
      throw new Error('Failed to fetch public tutorials');
    }

    res.json({
      success: true,
      data: result.tutorials,
      pagination: {
        page: result.page,
        limit: query.limit || 10,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  })
);

// POST /api/tutorials - Create new tutorial
router.post('/',
  validateRequest(createTutorialSchema),
  asyncHandler(async (req: any, res: Response) => {
    const tutorialData: CreateTutorialRequest = req.body;
    const userId = req.user.id;

    // If teamId is provided, check if user is a member
    if (tutorialData.teamId) {
      const membership = await databaseService.getTeamMembership(userId, tutorialData.teamId);
      if (!membership) {
        throw new AuthorizationError('You are not a member of this team');
      }
    }

    const tutorial = await databaseService.createTutorial(userId, tutorialData);

    // Invalidate cache
    await redisService.invalidatePattern(`tutorials:${userId}:*`);
    if (tutorial.isPublic) {
      await redisService.invalidatePattern('tutorials:public:*');
    }

    logger.info('Tutorial created', {
      tutorialId: tutorial.id,
      userId,
      title: tutorial.title,
    });

    res.status(201).json({
      success: true,
      data: tutorial,
      message: 'Tutorial created successfully',
    });
  })
);

// GET /api/tutorials/:id - Get specific tutorial
router.get('/:id',
  optionalAuthMiddleware,
  validateParams(commonSchemas.idParam),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    const cacheKey = `tutorial:${id}`;
    
    const tutorial = await redisService.cache(
      cacheKey,
      () => databaseService.getTutorialById(id),
      300 // 5 minutes cache
    );

    if (!tutorial) {
      throw new NotFoundError('Tutorial not found');
    }

    // Check access permissions
    const hasAccess = tutorial.isPublic || 
                     tutorial.authorId === userId ||
                     (tutorial.teamId && userId && await databaseService.getTeamMembership(userId, tutorial.teamId));

    if (!hasAccess) {
      throw new AuthorizationError('Access denied');
    }

    // Increment view count (async, don't wait)
    databaseService.incrementTutorialViews(id).catch(error => {
      logger.error('Failed to increment tutorial views:', error);
    });

    logger.info('Tutorial viewed', {
      tutorialId: id,
      userId,
      title: tutorial.title,
    });

    res.json({
      success: true,
      data: tutorial,
    });
  })
);

// PUT /api/tutorials/:id - Update tutorial
router.put('/:id',
  validateParams(commonSchemas.idParam),
  validateRequest(updateTutorialSchema),
  requireOwnership('tutorial'),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const updateData: UpdateTutorialRequest = req.body;
    const userId = req.user.id;

    const tutorial = await databaseService.updateTutorial(id, updateData);

    // Invalidate cache
    await redisService.del(`tutorial:${id}`);
    await redisService.invalidatePattern(`tutorials:${userId}:*`);
    if (tutorial.isPublic) {
      await redisService.invalidatePattern('tutorials:public:*');
    }

    // Notify collaborators via WebSocket
    // TODO: Get WebSocket service instance and broadcast update

    logger.info('Tutorial updated', {
      tutorialId: id,
      userId,
      changes: Object.keys(updateData),
    });

    res.json({
      success: true,
      data: tutorial,
      message: 'Tutorial updated successfully',
    });
  })
);

// DELETE /api/tutorials/:id - Delete tutorial
router.delete('/:id',
  validateParams(commonSchemas.idParam),
  requireOwnership('tutorial'),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id;

    // Get tutorial before deletion for cleanup
    const tutorial = await databaseService.getTutorialById(id);
    if (!tutorial) {
      throw new NotFoundError('Tutorial not found');
    }

    await databaseService.deleteTutorial(id);

    // Invalidate cache
    await redisService.del(`tutorial:${id}`);
    await redisService.invalidatePattern(`tutorials:${userId}:*`);
    if (tutorial.isPublic) {
      await redisService.invalidatePattern('tutorials:public:*');
    }

    // TODO: Delete associated files from storage

    logger.info('Tutorial deleted', {
      tutorialId: id,
      userId,
      title: tutorial.title,
    });

    res.json({
      success: true,
      message: 'Tutorial deleted successfully',
    });
  })
);

// POST /api/tutorials/:id/duplicate - Duplicate tutorial
router.post('/:id/duplicate',
  validateParams(commonSchemas.idParam),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id;

    const originalTutorial = await databaseService.getTutorialById(id);
    if (!originalTutorial) {
      throw new NotFoundError('Tutorial not found');
    }

    // Check access permissions
    const hasAccess = originalTutorial.isPublic || 
                     originalTutorial.authorId === userId ||
                     (originalTutorial.teamId && await databaseService.getTeamMembership(userId, originalTutorial.teamId));

    if (!hasAccess) {
      throw new AuthorizationError('Access denied');
    }

    // Create duplicate
    const duplicateData: CreateTutorialRequest = {
      title: `${originalTutorial.title} (Copy)`,
      description: originalTutorial.description,
      tags: originalTutorial.tags,
      category: originalTutorial.category,
      isPublic: false, // Always create as private
    };

    const duplicate = await databaseService.createTutorial(userId, duplicateData);

    // TODO: Duplicate steps and their screenshots

    // Invalidate cache
    await redisService.invalidatePattern(`tutorials:${userId}:*`);

    logger.info('Tutorial duplicated', {
      originalId: id,
      duplicateId: duplicate.id,
      userId,
    });

    res.status(201).json({
      success: true,
      data: duplicate,
      message: 'Tutorial duplicated successfully',
    });
  })
);

// POST /api/tutorials/:id/share - Generate share link
router.post('/:id/share',
  validateParams(commonSchemas.idParam),
  validateRequest(shareSettingsSchema),
  requireOwnership('tutorial'),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const shareSettings = req.body;
    const userId = req.user.id;

    // Generate share token
    const shareToken = require('crypto').randomBytes(32).toString('hex');

    const tutorial = await databaseService.updateTutorial(id, {
      shareToken,
      shareSettings,
    });

    // Invalidate cache
    await redisService.del(`tutorial:${id}`);

    logger.info('Tutorial share link generated', {
      tutorialId: id,
      userId,
      shareToken,
    });

    res.json({
      success: true,
      data: {
        shareUrl: `${req.protocol}://${req.get('host')}/shared/${shareToken}`,
        shareToken,
        settings: shareSettings,
      },
      message: 'Share link generated successfully',
    });
  })
);

// DELETE /api/tutorials/:id/share - Revoke share link
router.delete('/:id/share',
  validateParams(commonSchemas.idParam),
  requireOwnership('tutorial'),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id;

    await databaseService.updateTutorial(id, {
      shareToken: null,
      shareSettings: {},
    });

    // Invalidate cache
    await redisService.del(`tutorial:${id}`);

    logger.info('Tutorial share link revoked', {
      tutorialId: id,
      userId,
    });

    res.json({
      success: true,
      message: 'Share link revoked successfully',
    });
  })
);

// GET /api/tutorials/:id/analytics - Get tutorial analytics
router.get('/:id/analytics',
  validateParams(commonSchemas.idParam),
  requireOwnership('tutorial'),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;

    // TODO: Implement analytics aggregation
    const analytics = {
      views: 0,
      uniqueViews: 0,
      completions: 0,
      averageTime: 0,
      dropoffPoints: [],
      demographics: {},
    };

    res.json({
      success: true,
      data: analytics,
    });
  })
);

export default router;
