import { Router, Response } from 'express';
import multer from 'multer';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs-extra';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { async<PERSON>and<PERSON> } from '@/middleware/error';
import { validateFileUpload } from '@/middleware/validation';
import { ValidationError } from '@/types';

const router = Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 5, // Max 5 files per request
  },
  fileFilter: (req, file, cb) => {
    // Allow only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Ensure upload directory exists
const uploadDir = path.join(process.cwd(), 'uploads');
fs.ensureDirSync(uploadDir);

// Helper function to process and save image
const processImage = async (buffer: Buffer, filename: string): Promise<{
  url: string;
  width: number;
  height: number;
  size: number;
  format: string;
}> => {
  const outputPath = path.join(uploadDir, filename);
  
  // Process image with sharp
  const processed = await sharp(buffer)
    .resize(1920, 1080, { 
      fit: 'inside',
      withoutEnlargement: true 
    })
    .jpeg({ 
      quality: 85,
      progressive: true 
    })
    .toFile(outputPath);

  return {
    url: `/uploads/${filename}`,
    width: processed.width,
    height: processed.height,
    size: processed.size,
    format: 'jpeg',
  };
};

// POST /api/upload/screenshot - Upload screenshot
router.post('/screenshot',
  upload.single('screenshot'),
  validateFileUpload(['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
  asyncHandler(async (req: any, res: Response) => {
    if (!req.file) {
      throw new ValidationError('No file uploaded');
    }

    const userId = req.user.id;
    const file = req.file;
    
    // Generate unique filename
    const filename = `${uuidv4()}.jpg`;
    
    try {
      // Process and save image
      const result = await processImage(file.buffer, filename);

      logger.info('Screenshot uploaded', {
        userId,
        filename,
        originalSize: file.size,
        processedSize: result.size,
        dimensions: `${result.width}x${result.height}`,
      });

      res.json({
        success: true,
        data: result,
        message: 'Screenshot uploaded successfully',
      });

    } catch (error) {
      logger.error('Failed to process screenshot:', error);
      throw new ValidationError('Failed to process image');
    }
  })
);

// POST /api/upload/multiple - Upload multiple images
router.post('/multiple',
  upload.array('images', 10),
  validateFileUpload(['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
  asyncHandler(async (req: any, res: Response) => {
    if (!req.files || req.files.length === 0) {
      throw new ValidationError('No files uploaded');
    }

    const userId = req.user.id;
    const files = req.files as Express.Multer.File[];
    
    const results = [];

    for (const file of files) {
      const filename = `${uuidv4()}.jpg`;
      
      try {
        const result = await processImage(file.buffer, filename);
        results.push({
          originalName: file.originalname,
          ...result,
        });
      } catch (error) {
        logger.error('Failed to process image:', { filename: file.originalname, error });
        // Continue with other files
      }
    }

    logger.info('Multiple images uploaded', {
      userId,
      count: results.length,
      totalOriginalSize: files.reduce((sum, f) => sum + f.size, 0),
      totalProcessedSize: results.reduce((sum, r) => sum + r.size, 0),
    });

    res.json({
      success: true,
      data: results,
      message: `${results.length} images uploaded successfully`,
    });
  })
);

// DELETE /api/upload/:filename - Delete uploaded file
router.delete('/:filename',
  asyncHandler(async (req: any, res: Response) => {
    const { filename } = req.params;
    const userId = req.user.id;

    // Validate filename to prevent directory traversal
    if (!/^[a-f0-9-]+\.(jpg|jpeg|png|gif|webp)$/i.test(filename)) {
      throw new ValidationError('Invalid filename');
    }

    const filePath = path.join(uploadDir, filename);

    try {
      await fs.remove(filePath);

      logger.info('File deleted', {
        userId,
        filename,
      });

      res.json({
        success: true,
        message: 'File deleted successfully',
      });

    } catch (error) {
      logger.error('Failed to delete file:', { filename, error });
      throw new ValidationError('Failed to delete file');
    }
  })
);

// GET /api/upload/info/:filename - Get file info
router.get('/info/:filename',
  asyncHandler(async (req: any, res: Response) => {
    const { filename } = req.params;

    // Validate filename
    if (!/^[a-f0-9-]+\.(jpg|jpeg|png|gif|webp)$/i.test(filename)) {
      throw new ValidationError('Invalid filename');
    }

    const filePath = path.join(uploadDir, filename);

    try {
      const stats = await fs.stat(filePath);
      const metadata = await sharp(filePath).metadata();

      res.json({
        success: true,
        data: {
          filename,
          size: stats.size,
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime,
        },
      });

    } catch (error) {
      throw new ValidationError('File not found');
    }
  })
);

export default router;
