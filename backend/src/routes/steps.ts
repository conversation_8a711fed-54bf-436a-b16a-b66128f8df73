import { Router, Response } from 'express';
import { z } from 'zod';
import { DatabaseService } from '@/services/database';
import { RedisService } from '@/services/redis';
import { logger } from '@/utils/logger';
import { asyncHandler } from '@/middleware/error';
import { validateRequest, validateParams, commonSchemas } from '@/middleware/validation';
import { requireOwnership } from '@/middleware/auth';
import type { CreateStepRequest, UpdateStepRequest } from '@/types';
import { NotFoundError } from '@/types';

const router = Router();
const databaseService = new DatabaseService();
const redisService = new RedisService();

// Validation schemas
const createStepSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  action: z.enum(['click', 'type', 'navigate', 'scroll', 'hover', 'select', 'submit', 'wait', 'custom']),
  element: z.string().max(500, 'Element selector must be less than 500 characters').optional(),
  elementText: z.string().max(200, 'Element text must be less than 200 characters').optional(),
  inputValue: z.string().max(1000, 'Input value must be less than 1000 characters').optional(),
  url: z.string().url('Invalid URL').optional(),
  screenshot: z.string().url('Invalid screenshot URL').optional(),
  annotations: z.array(z.object({
    id: z.string(),
    type: z.enum(['arrow', 'highlight', 'blur', 'text', 'circle', 'rectangle']),
    x: z.number(),
    y: z.number(),
    width: z.number().optional(),
    height: z.number().optional(),
    text: z.string().optional(),
    color: z.string().optional(),
    style: z.record(z.any()).optional(),
  })).optional(),
  metadata: z.record(z.any()).optional(),
  timing: z.number().min(0).optional(),
});

const updateStepSchema = createStepSchema.partial().extend({
  order: z.number().min(1).optional(),
});

const reorderStepsSchema = z.object({
  steps: z.array(z.object({
    id: z.string().cuid('Invalid step ID'),
    order: z.number().min(1),
  })),
});

// GET /api/steps/tutorial/:tutorialId - Get all steps for a tutorial
router.get('/tutorial/:tutorialId',
  validateParams(z.object({ tutorialId: z.string().cuid('Invalid tutorial ID') })),
  asyncHandler(async (req: any, res: Response) => {
    const { tutorialId } = req.params;
    const userId = req.user.id;

    // Check if user has access to the tutorial
    const tutorial = await databaseService.getTutorialById(tutorialId);
    if (!tutorial) {
      throw new NotFoundError('Tutorial not found');
    }

    const hasAccess = tutorial.isPublic || 
                     tutorial.authorId === userId ||
                     (tutorial.teamId && await databaseService.getTeamMembership(userId, tutorial.teamId));

    if (!hasAccess) {
      throw new NotFoundError('Tutorial not found');
    }

    const cacheKey = `steps:tutorial:${tutorialId}`;
    
    const steps = await redisService.cache(
      cacheKey,
      () => databaseService.getStepsByTutorialId(tutorialId),
      300 // 5 minutes cache
    );

    res.json({
      success: true,
      data: steps,
    });
  })
);

// POST /api/steps/tutorial/:tutorialId - Create new step
router.post('/tutorial/:tutorialId',
  validateParams(z.object({ tutorialId: z.string().cuid('Invalid tutorial ID') })),
  validateRequest(createStepSchema),
  requireOwnership('tutorial'),
  asyncHandler(async (req: any, res: Response) => {
    const { tutorialId } = req.params;
    const stepData: CreateStepRequest = req.body;
    const userId = req.user.id;

    const step = await databaseService.createStep(tutorialId, stepData);

    // Invalidate cache
    await redisService.del(`steps:tutorial:${tutorialId}`);
    await redisService.del(`tutorial:${tutorialId}`);

    logger.info('Step created', {
      stepId: step.id,
      tutorialId,
      userId,
      title: step.title,
    });

    res.status(201).json({
      success: true,
      data: step,
      message: 'Step created successfully',
    });
  })
);

// GET /api/steps/:id - Get specific step
router.get('/:id',
  validateParams(commonSchemas.idParam),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id;

    const step = await databaseService.getStepById(id);
    if (!step) {
      throw new NotFoundError('Step not found');
    }

    // Check access through tutorial
    const tutorial = await databaseService.getTutorialById(step.tutorialId);
    if (!tutorial) {
      throw new NotFoundError('Tutorial not found');
    }

    const hasAccess = tutorial.isPublic || 
                     tutorial.authorId === userId ||
                     (tutorial.teamId && await databaseService.getTeamMembership(userId, tutorial.teamId));

    if (!hasAccess) {
      throw new NotFoundError('Step not found');
    }

    res.json({
      success: true,
      data: step,
    });
  })
);

// PUT /api/steps/:id - Update step
router.put('/:id',
  validateParams(commonSchemas.idParam),
  validateRequest(updateStepSchema),
  requireOwnership('step'),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const updateData: UpdateStepRequest = req.body;
    const userId = req.user.id;

    const step = await databaseService.updateStep(id, updateData);

    // Invalidate cache
    await redisService.del(`steps:tutorial:${step.tutorialId}`);
    await redisService.del(`tutorial:${step.tutorialId}`);

    logger.info('Step updated', {
      stepId: id,
      tutorialId: step.tutorialId,
      userId,
      changes: Object.keys(updateData),
    });

    res.json({
      success: true,
      data: step,
      message: 'Step updated successfully',
    });
  })
);

// DELETE /api/steps/:id - Delete step
router.delete('/:id',
  validateParams(commonSchemas.idParam),
  requireOwnership('step'),
  asyncHandler(async (req: any, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id;

    const step = await databaseService.getStepById(id);
    if (!step) {
      throw new NotFoundError('Step not found');
    }

    await databaseService.deleteStep(id);

    // Invalidate cache
    await redisService.del(`steps:tutorial:${step.tutorialId}`);
    await redisService.del(`tutorial:${step.tutorialId}`);

    logger.info('Step deleted', {
      stepId: id,
      tutorialId: step.tutorialId,
      userId,
    });

    res.json({
      success: true,
      message: 'Step deleted successfully',
    });
  })
);

// POST /api/steps/tutorial/:tutorialId/reorder - Reorder steps
router.post('/tutorial/:tutorialId/reorder',
  validateParams(z.object({ tutorialId: z.string().cuid('Invalid tutorial ID') })),
  validateRequest(reorderStepsSchema),
  requireOwnership('tutorial'),
  asyncHandler(async (req: any, res: Response) => {
    const { tutorialId } = req.params;
    const { steps } = req.body;
    const userId = req.user.id;

    await databaseService.reorderSteps(tutorialId, steps);

    // Invalidate cache
    await redisService.del(`steps:tutorial:${tutorialId}`);
    await redisService.del(`tutorial:${tutorialId}`);

    logger.info('Steps reordered', {
      tutorialId,
      userId,
      stepCount: steps.length,
    });

    res.json({
      success: true,
      message: 'Steps reordered successfully',
    });
  })
);

export default router;
