import { Router, Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { DatabaseService } from '@/services/database';
import { RedisService } from '@/services/redis';
import { asyncHandler } from '@/middleware/error';
import { validateRequest } from '@/middleware/validation';
import { authMiddleware } from '@/middleware/auth';
import type { RegisterRequest, LoginRequest, AuthResponse, User } from '@/types';
import { ValidationError, AuthenticationError, ConflictError } from '@/types';

const router = Router();
const databaseService = new DatabaseService();
const redisService = new RedisService();

// Validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  username: z.string().min(3, 'Username must be at least 3 characters').optional(),
});

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Helper functions
const generateTokens = async (user: User): Promise<{ accessToken: string; refreshToken: string; expiresIn: number }> => {
  // Create session
  const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  const session = await databaseService.createSession(
    user.id,
    jwt.sign({ userId: user.id }, config.jwt.refreshSecret),
    sessionExpiresAt
  );

  // Generate access token
  const accessToken = jwt.sign(
    { 
      userId: user.id, 
      sessionId: session.id,
      email: user.email 
    },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );

  // Generate refresh token
  const refreshToken = jwt.sign(
    { 
      userId: user.id, 
      sessionId: session.id,
      type: 'refresh' 
    },
    config.jwt.refreshSecret,
    { expiresIn: config.jwt.refreshExpiresIn }
  );

  // Store refresh token in Redis
  await redisService.set(
    `refresh_token:${user.id}:${session.id}`,
    refreshToken,
    7 * 24 * 60 * 60 // 7 days in seconds
  );

  const expiresIn = 15 * 60; // 15 minutes in seconds

  return { accessToken, refreshToken, expiresIn };
};

const sanitizeUser = (user: User): Omit<User, 'passwordHash'> => {
  const { passwordHash, ...sanitized } = user as any;
  return sanitized;
};

// Routes

// POST /api/auth/register
router.post('/register', validateRequest(registerSchema), asyncHandler(async (req: Request, res: Response) => {
  const { email, password, firstName, lastName, username }: RegisterRequest = req.body;

  // Check if user already exists
  const existingUser = await databaseService.getUserByEmail(email);
  if (existingUser) {
    throw new ConflictError('User with this email already exists');
  }

  // Check username uniqueness if provided
  if (username) {
    const existingUsername = await databaseService.getUserByEmail(username); // This would need a getUserByUsername method
    if (existingUsername) {
      throw new ConflictError('Username already taken');
    }
  }

  // Hash password
  const passwordHash = await bcrypt.hash(password, 12);

  // Create user
  const user = await databaseService.createUser({
    email,
    passwordHash,
    firstName,
    lastName,
    username,
  });

  // Generate tokens
  const { accessToken, refreshToken, expiresIn } = await generateTokens(user);

  // Update last login
  await databaseService.updateUser(user.id, { lastLoginAt: new Date() });

  logger.info('User registered successfully', {
    userId: user.id,
    email: user.email,
  });

  const response: AuthResponse = {
    user: sanitizeUser(user),
    accessToken,
    refreshToken,
    expiresIn,
  };

  res.status(201).json({
    success: true,
    data: response,
    message: 'User registered successfully',
  });
}));

// POST /api/auth/login
router.post('/login', validateRequest(loginSchema), asyncHandler(async (req: Request, res: Response) => {
  const { email, password }: LoginRequest = req.body;

  // Get user by email
  const user = await databaseService.getUserByEmail(email);
  if (!user) {
    throw new AuthenticationError('Invalid email or password');
  }

  // Check if user is active
  if (!user.isActive) {
    throw new AuthenticationError('Account is deactivated');
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, (user as any).passwordHash);
  if (!isPasswordValid) {
    throw new AuthenticationError('Invalid email or password');
  }

  // Generate tokens
  const { accessToken, refreshToken, expiresIn } = await generateTokens(user);

  // Update last login
  await databaseService.updateUser(user.id, { lastLoginAt: new Date() });

  logger.info('User logged in successfully', {
    userId: user.id,
    email: user.email,
  });

  const response: AuthResponse = {
    user: sanitizeUser(user),
    accessToken,
    refreshToken,
    expiresIn,
  };

  res.json({
    success: true,
    data: response,
    message: 'Login successful',
  });
}));

// POST /api/auth/refresh
router.post('/refresh', validateRequest(refreshTokenSchema), asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  try {
    // Verify refresh token
    const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret) as any;
    
    if (decoded.type !== 'refresh') {
      throw new AuthenticationError('Invalid token type');
    }

    // Check if refresh token exists in Redis
    const storedToken = await redisService.get(`refresh_token:${decoded.userId}:${decoded.sessionId}`);
    if (!storedToken || storedToken !== refreshToken) {
      throw new AuthenticationError('Invalid refresh token');
    }

    // Get user
    const user = await databaseService.getUserById(decoded.userId);
    if (!user || !user.isActive) {
      throw new AuthenticationError('User not found or inactive');
    }

    // Get session
    const session = await databaseService.getSessionById(decoded.sessionId);
    if (!session || session.expiresAt < new Date()) {
      throw new AuthenticationError('Session expired');
    }

    // Generate new access token
    const accessToken = jwt.sign(
      { 
        userId: user.id, 
        sessionId: session.id,
        email: user.email 
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    const expiresIn = 15 * 60; // 15 minutes in seconds

    res.json({
      success: true,
      data: {
        accessToken,
        expiresIn,
      },
      message: 'Token refreshed successfully',
    });

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new AuthenticationError('Invalid refresh token');
    }
    throw error;
  }
}));

// POST /api/auth/logout
router.post('/logout', authMiddleware, asyncHandler(async (req: any, res: Response) => {
  const { user, session } = req;

  // Delete session from database
  await databaseService.deleteSession(session.id);

  // Delete refresh token from Redis
  await redisService.del(`refresh_token:${user.id}:${session.id}`);

  logger.info('User logged out successfully', {
    userId: user.id,
    sessionId: session.id,
  });

  res.json({
    success: true,
    message: 'Logout successful',
  });
}));

// GET /api/auth/me
router.get('/me', authMiddleware, asyncHandler(async (req: any, res: Response) => {
  const { user } = req;

  res.json({
    success: true,
    data: sanitizeUser(user),
  });
}));

// POST /api/auth/forgot-password
router.post('/forgot-password', validateRequest(forgotPasswordSchema), asyncHandler(async (req: Request, res: Response) => {
  const { email } = req.body;

  const user = await databaseService.getUserByEmail(email);
  if (!user) {
    // Don't reveal if email exists
    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
    });
    return;
  }

  // Generate reset token
  const resetToken = jwt.sign(
    { userId: user.id, type: 'password_reset' },
    config.jwt.secret,
    { expiresIn: '1h' }
  );

  // Store reset token in database
  await databaseService.updateUser(user.id, {
    passwordResetToken: resetToken,
    passwordResetExpires: new Date(Date.now() + 60 * 60 * 1000), // 1 hour
  });

  // TODO: Send email with reset link
  logger.info('Password reset requested', {
    userId: user.id,
    email: user.email,
  });

  res.json({
    success: true,
    message: 'If an account with that email exists, a password reset link has been sent.',
  });
}));

// POST /api/auth/reset-password
router.post('/reset-password', validateRequest(resetPasswordSchema), asyncHandler(async (req: Request, res: Response) => {
  const { token, password } = req.body;

  try {
    // Verify reset token
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    
    if (decoded.type !== 'password_reset') {
      throw new AuthenticationError('Invalid token type');
    }

    // Get user and verify token
    const user = await databaseService.getUserById(decoded.userId);
    if (!user || 
        (user as any).passwordResetToken !== token || 
        !(user as any).passwordResetExpires || 
        (user as any).passwordResetExpires < new Date()) {
      throw new AuthenticationError('Invalid or expired reset token');
    }

    // Hash new password
    const passwordHash = await bcrypt.hash(password, 12);

    // Update user password and clear reset token
    await databaseService.updateUser(user.id, {
      passwordHash,
      passwordResetToken: null,
      passwordResetExpires: null,
    } as any);

    logger.info('Password reset successfully', {
      userId: user.id,
      email: user.email,
    });

    res.json({
      success: true,
      message: 'Password reset successfully',
    });

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new AuthenticationError('Invalid reset token');
    }
    throw error;
  }
}));

export default router;
