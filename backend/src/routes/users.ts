import { Router, Response } from 'express';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { DatabaseService } from '@/services/database';
import { RedisService } from '@/services/redis';
import { logger } from '@/utils/logger';
import { async<PERSON>and<PERSON> } from '@/middleware/error';
import { validateRequest, validateParams, commonSchemas } from '@/middleware/validation';
import { ConflictError, ValidationError } from '@/types';

const router = Router();
const databaseService = new DatabaseService();
const redisService = new RedisService();

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters').optional(),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters').optional(),
  username: z.string().min(3, 'Username must be at least 3 characters').max(30, 'Username must be less than 30 characters').optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  timezone: z.string().optional(),
  language: z.string().length(2, 'Language must be a 2-character code').optional(),
});

const updatePreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).optional(),
  notifications: z.object({
    email: z.boolean().optional(),
    push: z.boolean().optional(),
    collaboration: z.boolean().optional(),
  }).optional(),
  editor: z.object({
    autoSave: z.boolean().optional(),
    showLineNumbers: z.boolean().optional(),
    fontSize: z.number().min(10).max(24).optional(),
  }).optional(),
});

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required'),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// GET /api/users/profile - Get current user profile
router.get('/profile',
  asyncHandler(async (req: any, res: Response) => {
    const user = req.user;

    // Remove sensitive information
    const { passwordHash, passwordResetToken, passwordResetExpires, emailVerificationToken, ...profile } = user as any;

    res.json({
      success: true,
      data: profile,
    });
  })
);

// PUT /api/users/profile - Update user profile
router.put('/profile',
  validateRequest(updateProfileSchema),
  asyncHandler(async (req: any, res: Response) => {
    const userId = req.user.id;
    const updateData = req.body;

    // Check username uniqueness if provided
    if (updateData.username) {
      const existingUser = await databaseService.getUserByEmail(updateData.username); // This would need getUserByUsername
      if (existingUser && existingUser.id !== userId) {
        throw new ConflictError('Username already taken');
      }
    }

    const updatedUser = await databaseService.updateUser(userId, updateData);

    // Remove sensitive information
    const { passwordHash, passwordResetToken, passwordResetExpires, emailVerificationToken, ...profile } = updatedUser as any;

    logger.info('User profile updated', {
      userId,
      changes: Object.keys(updateData),
    });

    res.json({
      success: true,
      data: profile,
      message: 'Profile updated successfully',
    });
  })
);

// PUT /api/users/preferences - Update user preferences
router.put('/preferences',
  validateRequest(updatePreferencesSchema),
  asyncHandler(async (req: any, res: Response) => {
    const userId = req.user.id;
    const preferences = req.body;

    // Merge with existing preferences
    const currentUser = await databaseService.getUserById(userId);
    const updatedPreferences = {
      ...currentUser?.preferences,
      ...preferences,
    };

    const updatedUser = await databaseService.updateUser(userId, {
      preferences: updatedPreferences,
    });

    logger.info('User preferences updated', {
      userId,
      preferences: Object.keys(preferences),
    });

    res.json({
      success: true,
      data: updatedUser.preferences,
      message: 'Preferences updated successfully',
    });
  })
);

// POST /api/users/change-password - Change password
router.post('/change-password',
  validateRequest(changePasswordSchema),
  asyncHandler(async (req: any, res: Response) => {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    // Get user with password hash
    const user = await databaseService.getUserById(userId);
    if (!user) {
      throw new ValidationError('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, (user as any).passwordHash);
    if (!isCurrentPasswordValid) {
      throw new ValidationError('Current password is incorrect');
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, 12);

    // Update password
    await databaseService.updateUser(userId, {
      passwordHash: newPasswordHash,
    } as any);

    logger.info('User password changed', { userId });

    res.json({
      success: true,
      message: 'Password changed successfully',
    });
  })
);

// GET /api/users/stats - Get user statistics
router.get('/stats',
  asyncHandler(async (req: any, res: Response) => {
    const userId = req.user.id;

    // TODO: Implement user statistics aggregation
    const stats = {
      totalTutorials: 0,
      publicTutorials: 0,
      totalViews: 0,
      totalSteps: 0,
      joinedAt: req.user.createdAt,
      lastActive: req.user.lastLoginAt,
    };

    res.json({
      success: true,
      data: stats,
    });
  })
);

// DELETE /api/users/account - Delete user account
router.delete('/account',
  validateRequest(z.object({
    password: z.string().min(1, 'Password is required'),
    confirmation: z.literal('DELETE', { errorMap: () => ({ message: 'Please type DELETE to confirm' }) }),
  })),
  asyncHandler(async (req: any, res: Response) => {
    const userId = req.user.id;
    const { password } = req.body;

    // Verify password
    const user = await databaseService.getUserById(userId);
    if (!user) {
      throw new ValidationError('User not found');
    }

    const isPasswordValid = await bcrypt.compare(password, (user as any).passwordHash);
    if (!isPasswordValid) {
      throw new ValidationError('Password is incorrect');
    }

    // TODO: Implement account deletion
    // 1. Delete all user's tutorials and steps
    // 2. Delete all user's sessions
    // 3. Delete user's files from storage
    // 4. Delete user record

    logger.info('User account deletion requested', { userId });

    res.json({
      success: true,
      message: 'Account deletion initiated. This process may take a few minutes.',
    });
  })
);

export default router;
