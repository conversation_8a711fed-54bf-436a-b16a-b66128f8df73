// Core Types for Scribe Pro Backend

export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  preferences: Record<string, any>;
  timezone: string;
  language: string;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  isActive: boolean;
}

export interface Tutorial {
  id: string;
  title: string;
  description?: string;
  steps: Step[];
  tags: string[];
  category?: string;
  isPublic: boolean;
  isTemplate: boolean;
  settings: Record<string, any>;
  shareToken?: string;
  shareSettings: Record<string, any>;
  viewCount: number;
  analytics: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  authorId: string;
  teamId?: string;
}

export interface Step {
  id: string;
  order: number;
  title: string;
  description?: string;
  action: ActionType;
  element?: string;
  elementText?: string;
  inputValue?: string;
  url?: string;
  screenshot?: string;
  annotations: Annotation[];
  metadata: Record<string, any>;
  timing?: number;
  tutorialId: string;
}

export type ActionType = 
  | 'click'
  | 'type'
  | 'navigate'
  | 'scroll'
  | 'hover'
  | 'select'
  | 'submit'
  | 'wait'
  | 'custom';

export interface Annotation {
  id: string;
  type: 'arrow' | 'highlight' | 'blur' | 'text' | 'circle' | 'rectangle';
  x: number;
  y: number;
  width?: number;
  height?: number;
  text?: string;
  color?: string;
  style?: Record<string, any>;
}

export interface Team {
  id: string;
  name: string;
  slug: string;
  description?: string;
  avatar?: string;
  settings: Record<string, any>;
  plan: 'free' | 'pro' | 'enterprise';
  createdAt: Date;
  updatedAt: Date;
}

export interface TeamMember {
  id: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: Date;
  userId: string;
  teamId: string;
}

export interface Session {
  id: string;
  token: string;
  data: Record<string, any>;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface ApiKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  createdAt: Date;
  lastUsedAt?: Date;
  expiresAt?: Date;
  isActive: boolean;
  userId: string;
}

// Request/Response Types
export interface AuthRequest extends Request {
  user?: User;
  session?: Session;
}

export interface CreateTutorialRequest {
  title: string;
  description?: string;
  tags?: string[];
  category?: string;
  isPublic?: boolean;
  teamId?: string;
}

export interface UpdateTutorialRequest {
  title?: string;
  description?: string;
  tags?: string[];
  category?: string;
  isPublic?: boolean;
  settings?: Record<string, any>;
}

export interface CreateStepRequest {
  title: string;
  description?: string;
  action: ActionType;
  element?: string;
  elementText?: string;
  inputValue?: string;
  url?: string;
  screenshot?: string;
  annotations?: Annotation[];
  metadata?: Record<string, any>;
  timing?: number;
}

export interface UpdateStepRequest {
  title?: string;
  description?: string;
  action?: ActionType;
  element?: string;
  elementText?: string;
  inputValue?: string;
  url?: string;
  screenshot?: string;
  annotations?: Annotation[];
  metadata?: Record<string, any>;
  timing?: number;
  order?: number;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  username?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TutorialQuery extends PaginationQuery {
  search?: string;
  tags?: string[];
  category?: string;
  isPublic?: boolean;
  authorId?: string;
  teamId?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: Date;
  userId?: string;
  tutorialId?: string;
}

export interface CollaborationEvent {
  type: 'step_updated' | 'step_created' | 'step_deleted' | 'tutorial_updated' | 'user_joined' | 'user_left';
  data: any;
  userId: string;
  tutorialId: string;
  timestamp: Date;
}

// File Upload Types
export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer: Buffer;
}

export interface ProcessedImage {
  url: string;
  width: number;
  height: number;
  size: number;
  format: string;
}

// Analytics Types
export interface AnalyticsEvent {
  event: string;
  properties: Record<string, any>;
  userId?: string;
  tutorialId?: string;
  timestamp: Date;
}

export interface TutorialAnalytics {
  views: number;
  uniqueViews: number;
  completions: number;
  averageTime: number;
  dropoffPoints: number[];
  demographics: Record<string, any>;
}

// Error Types
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429);
  }
}
