{"name": "scribe-pro-backend", "version": "2.0.0", "description": "Advanced backend API for Scribe Pro tutorial recording platform", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "tsx watch src/server.ts", "build": "tsc && tsc-alias", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx src/database/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "@prisma/client": "^5.7.0", "prisma": "^5.7.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "zod": "^3.22.4", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "redis": "^4.6.11", "aws-sdk": "^2.1506.0", "sharp": "^0.33.0", "socket.io": "^4.7.4", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "nodemailer": "^6.9.7"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/nodemailer": "^6.4.14", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.0", "tsx": "^4.6.0", "tsc-alias": "^1.8.8", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "nodemon": "^3.0.1"}}