// Prisma Schema for Scribe Pro
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  firstName String?
  lastName  String?
  avatar    String?
  
  // Authentication
  passwordHash String
  emailVerified <PERSON>olean @default(false)
  emailVerificationToken String?
  passwordResetToken String?
  passwordResetExpires DateTime?
  
  // Preferences
  preferences Json @default("{}")
  timezone String @default("UTC")
  language String @default("en")
  
  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLoginAt DateTime?
  isActive Boolean @default(true)
  
  // Relationships
  tutorials Tutorial[]
  teamMemberships TeamMember[]
  sessions Session[]
  apiKeys ApiKey[]
  
  @@map("users")
}

// Team/Organization Management
model Team {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  avatar      String?
  
  // Settings
  settings    Json @default("{}")
  plan        String @default("free") // free, pro, enterprise
  
  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  members     TeamMember[]
  tutorials   Tutorial[]
  
  @@map("teams")
}

model TeamMember {
  id     String @id @default(cuid())
  role   String @default("member") // owner, admin, member, viewer
  
  // Metadata
  joinedAt DateTime @default(now())
  
  // Relationships
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId String
  
  @@unique([userId, teamId])
  @@map("team_members")
}

// Tutorial Management
model Tutorial {
  id          String   @id @default(cuid())
  title       String
  description String?
  
  // Content
  steps       Step[]
  tags        String[]
  category    String?
  
  // Settings
  isPublic    Boolean @default(false)
  isTemplate  Boolean @default(false)
  settings    Json @default("{}")
  
  // Sharing
  shareToken  String? @unique
  shareSettings Json @default("{}")
  
  // Analytics
  viewCount   Int @default(0)
  analytics   Json @default("{}")
  
  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime?
  
  // Relationships
  author      User   @relation(fields: [authorId], references: [id])
  authorId    String
  team        Team?  @relation(fields: [teamId], references: [id])
  teamId      String?
  versions    TutorialVersion[]
  
  @@map("tutorials")
}

// Tutorial Steps
model Step {
  id          String   @id @default(cuid())
  order       Int
  title       String
  description String?
  
  // Action Details
  action      String   // click, type, navigate, scroll, hover, select, etc.
  element     String?  // CSS selector or element description
  elementText String?  // Text content of the element
  inputValue  String?  // Value for input actions
  url         String?  // URL for navigation actions
  
  // Screenshot & Media
  screenshot  String?  // URL to screenshot
  annotations Json @default("[]") // Array of annotation objects
  
  // Metadata
  metadata    Json @default("{}")
  timing      Int? // Time spent on this step (ms)
  
  // Relationships
  tutorial    Tutorial @relation(fields: [tutorialId], references: [id], onDelete: Cascade)
  tutorialId  String
  
  @@unique([tutorialId, order])
  @@map("steps")
}

// Version Control
model TutorialVersion {
  id          String   @id @default(cuid())
  version     String
  title       String
  description String?
  content     Json     // Serialized tutorial content
  
  // Metadata
  createdAt   DateTime @default(now())
  createdBy   String
  
  // Relationships
  tutorial    Tutorial @relation(fields: [tutorialId], references: [id], onDelete: Cascade)
  tutorialId  String
  
  @@map("tutorial_versions")
}

// Session Management
model Session {
  id        String   @id @default(cuid())
  token     String   @unique
  
  // Session Data
  data      Json @default("{}")
  expiresAt DateTime
  
  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  user      User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  
  @@map("sessions")
}

// API Keys for integrations
model ApiKey {
  id          String   @id @default(cuid())
  name        String
  key         String   @unique
  permissions String[] // Array of permissions
  
  // Metadata
  createdAt   DateTime @default(now())
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  isActive    Boolean @default(true)
  
  // Relationships
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String
  
  @@map("api_keys")
}
