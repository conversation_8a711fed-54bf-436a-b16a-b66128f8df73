#!/usr/bin/env python3
import json
import re
import os

def extract_markdown_content(html_file):
    """Extract markdown content from HTML file containing JSON data."""
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the JSON data containing the markdown content
    json_match = re.search(r'"rawLines":\[(.*?)\],', content, re.DOTALL)
    if not json_match:
        print(f"Could not find rawLines in {html_file}")
        return None
    
    # Extract the raw lines array
    raw_lines_str = json_match.group(1)
    
    # Parse the JSON array of strings
    try:
        # Clean up the string to make it valid JSON
        raw_lines_str = raw_lines_str.replace('\\u003c', '<').replace('\\u003e', '>').replace('\\u0026', '&')
        raw_lines_str = raw_lines_str.replace('\\"', '"')
        
        # Split by commas and clean up each line
        lines = []
        current_line = ""
        in_string = False
        escape_next = False
        
        for char in raw_lines_str:
            if escape_next:
                current_line += char
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                current_line += char
                continue
                
            if char == '"' and not escape_next:
                in_string = not in_string
                if not in_string:  # End of string
                    lines.append(current_line)
                    current_line = ""
                continue
                
            if in_string:
                current_line += char
        
        # Join the lines to create the markdown content
        markdown_content = '\n'.join(lines)
        return markdown_content
        
    except Exception as e:
        print(f"Error parsing {html_file}: {e}")
        return None

def main():
    # List of files to process
    files = [
        'landing-components.mdc',
        'project-structure.mdc', 
        'self-improve.mdc',
        'tech-stack.mdc',
        'cursor-rules.mdc'
    ]
    
    # Create .cursor/rules directory if it doesn't exist
    os.makedirs('.cursor/rules', exist_ok=True)
    
    for file in files:
        if os.path.exists(file):
            print(f"Processing {file}...")
            markdown_content = extract_markdown_content(file)
            
            if markdown_content:
                # Write to .cursor/rules directory
                output_file = f'.cursor/rules/{file}'
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                print(f"Created {output_file}")
            else:
                print(f"Failed to extract content from {file}")
        else:
            print(f"File {file} not found")

if __name__ == "__main__":
    main() 