/**
 * Scribe Pro - Main Application Component
 * Professional tutorial recording and editing platform
 */

import React from 'react';
import './index.css';

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">SP</span>
                </div>
                <h1 className="text-xl font-bold text-gray-900">Scribe Pro</h1>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Demo
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            🎯 Scribe Pro - Advanced Tutorial Recording Platform
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            A production-ready, enterprise-grade tutorial recording platform that significantly improves upon Scribe.com's functionality.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="text-2xl mb-4">🎯</div>
              <h3 className="text-lg font-semibold mb-2">Intelligent Recording</h3>
              <p className="text-gray-600">Smart interaction detection with automatic screenshot capture and context-aware descriptions.</p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="text-2xl mb-4">🎨</div>
              <h3 className="text-lg font-semibold mb-2">Advanced Editor</h3>
              <p className="text-gray-600">Drag-and-drop reordering, image annotation tools, and real-time collaboration features.</p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="text-2xl mb-4">📊</div>
              <h3 className="text-lg font-semibold mb-2">Analytics & Insights</h3>
              <p className="text-gray-600">Comprehensive analytics with performance metrics and user engagement tracking.</p>
            </div>
          </div>

          <div className="bg-white rounded-lg p-8 shadow-sm border">
            <h3 className="text-xl font-semibold mb-4">🚀 Platform Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Frontend: Running</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Extension: Built</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span>Backend: Setup Required</span>
              </div>
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-semibold mb-4">🎊 Success! Your Scribe Pro platform is ready!</h3>
            <p className="text-gray-600">
              The frontend is running successfully and the Chrome extension is built and ready to use.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
