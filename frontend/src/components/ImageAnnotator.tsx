/**
 * Image Annotator Component
 * Canvas-based tool for adding annotations to screenshots
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  ArrowRight,
  Square,
  Circle,
  Type,
  Highlighter,
  Blur,
  Palette,
  Undo,
  Redo,
  Save,
  X
} from 'lucide-react';
import type { Step, Annotation } from '@/types';
import { Button } from './ui/Button';
import { v4 as uuidv4 } from 'uuid';

interface ImageAnnotatorProps {
  step: Step;
  onSave: (annotations: Annotation[]) => void;
  onCancel: () => void;
}

interface AnnotationTool {
  type: Annotation['type'];
  icon: React.ReactNode;
  label: string;
}

interface DrawingState {
  isDrawing: boolean;
  startX: number;
  startY: number;
  currentAnnotation: Partial<Annotation> | null;
}

export const ImageAnnotator: React.FC<ImageAnnotatorProps> = ({ step, onSave, onCancel }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [annotations, setAnnotations] = useState<Annotation[]>(step.annotations || []);
  const [selectedTool, setSelectedTool] = useState<Annotation['type']>('arrow');
  const [selectedColor, setSelectedColor] = useState('#3b82f6');
  const [strokeWidth, setStrokeWidth] = useState(3);
  const [drawingState, setDrawingState] = useState<DrawingState>({
    isDrawing: false,
    startX: 0,
    startY: 0,
    currentAnnotation: null,
  });
  const [history, setHistory] = useState<Annotation[][]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [imageLoaded, setImageLoaded] = useState(false);

  const tools: AnnotationTool[] = [
    { type: 'arrow', icon: <ArrowRight className="w-4 h-4" />, label: 'Arrow' },
    { type: 'rectangle', icon: <Square className="w-4 h-4" />, label: 'Rectangle' },
    { type: 'circle', icon: <Circle className="w-4 h-4" />, label: 'Circle' },
    { type: 'highlight', icon: <Highlighter className="w-4 h-4" />, label: 'Highlight' },
    { type: 'blur', icon: <Blur className="w-4 h-4" />, label: 'Blur' },
    { type: 'text', icon: <Type className="w-4 h-4" />, label: 'Text' },
  ];

  const colors = [
    '#3b82f6', // Blue
    '#ef4444', // Red
    '#10b981', // Green
    '#f59e0b', // Yellow
    '#8b5cf6', // Purple
    '#f97316', // Orange
    '#06b6d4', // Cyan
    '#84cc16', // Lime
  ];

  // Initialize canvas and load image
  useEffect(() => {
    if (!step.screenshot) return;

    const image = new Image();
    image.crossOrigin = 'anonymous';
    image.onload = () => {
      setImageLoaded(true);
      redrawCanvas();
    };
    image.src = step.screenshot;
    imageRef.current = image;
  }, [step.screenshot]);

  // Redraw canvas with image and annotations
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    if (!canvas || !image || !imageLoaded) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match image
    canvas.width = image.naturalWidth;
    canvas.height = image.naturalHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw image
    ctx.drawImage(image, 0, 0);

    // Draw annotations
    annotations.forEach(annotation => {
      drawAnnotation(ctx, annotation);
    });

    // Draw current annotation if drawing
    if (drawingState.currentAnnotation) {
      drawAnnotation(ctx, drawingState.currentAnnotation as Annotation);
    }
  }, [annotations, drawingState.currentAnnotation, imageLoaded]);

  // Draw individual annotation
  const drawAnnotation = useCallback((ctx: CanvasRenderingContext2D, annotation: Annotation) => {
    ctx.save();
    ctx.strokeStyle = annotation.color || selectedColor;
    ctx.fillStyle = annotation.color || selectedColor;
    ctx.lineWidth = strokeWidth;

    switch (annotation.type) {
      case 'arrow':
        drawArrow(ctx, annotation);
        break;
      case 'rectangle':
        ctx.strokeRect(annotation.x, annotation.y, annotation.width || 0, annotation.height || 0);
        break;
      case 'circle':
        const radius = Math.sqrt(Math.pow(annotation.width || 0, 2) + Math.pow(annotation.height || 0, 2)) / 2;
        ctx.beginPath();
        ctx.arc(annotation.x + (annotation.width || 0) / 2, annotation.y + (annotation.height || 0) / 2, radius, 0, 2 * Math.PI);
        ctx.stroke();
        break;
      case 'highlight':
        ctx.globalAlpha = 0.3;
        ctx.fillRect(annotation.x, annotation.y, annotation.width || 0, annotation.height || 0);
        ctx.globalAlpha = 1;
        break;
      case 'blur':
        // Apply blur effect (simplified)
        ctx.filter = 'blur(10px)';
        ctx.drawImage(canvasRef.current!, annotation.x, annotation.y, annotation.width || 0, annotation.height || 0, annotation.x, annotation.y, annotation.width || 0, annotation.height || 0);
        ctx.filter = 'none';
        break;
      case 'text':
        if (annotation.text) {
          ctx.font = '16px Arial';
          ctx.fillText(annotation.text, annotation.x, annotation.y);
        }
        break;
    }

    ctx.restore();
  }, [selectedColor, strokeWidth]);

  // Draw arrow
  const drawArrow = useCallback((ctx: CanvasRenderingContext2D, annotation: Annotation) => {
    const headLength = 15;
    const headAngle = Math.PI / 6;
    
    const dx = (annotation.width || 0);
    const dy = (annotation.height || 0);
    const angle = Math.atan2(dy, dx);

    // Draw line
    ctx.beginPath();
    ctx.moveTo(annotation.x, annotation.y);
    ctx.lineTo(annotation.x + dx, annotation.y + dy);
    ctx.stroke();

    // Draw arrowhead
    ctx.beginPath();
    ctx.moveTo(annotation.x + dx, annotation.y + dy);
    ctx.lineTo(
      annotation.x + dx - headLength * Math.cos(angle - headAngle),
      annotation.y + dy - headLength * Math.sin(angle - headAngle)
    );
    ctx.moveTo(annotation.x + dx, annotation.y + dy);
    ctx.lineTo(
      annotation.x + dx - headLength * Math.cos(angle + headAngle),
      annotation.y + dy - headLength * Math.sin(angle + headAngle)
    );
    ctx.stroke();
  }, []);

  // Handle mouse events
  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    const x = (event.clientX - rect.left) * scaleX;
    const y = (event.clientY - rect.top) * scaleY;

    if (selectedTool === 'text') {
      const text = prompt('Enter text:');
      if (text) {
        const newAnnotation: Annotation = {
          id: uuidv4(),
          type: 'text',
          x,
          y,
          text,
          color: selectedColor,
        };
        addAnnotation(newAnnotation);
      }
      return;
    }

    setDrawingState({
      isDrawing: true,
      startX: x,
      startY: y,
      currentAnnotation: {
        id: uuidv4(),
        type: selectedTool,
        x,
        y,
        width: 0,
        height: 0,
        color: selectedColor,
      },
    });
  }, [selectedTool, selectedColor]);

  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!drawingState.isDrawing || !drawingState.currentAnnotation) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    const x = (event.clientX - rect.left) * scaleX;
    const y = (event.clientY - rect.top) * scaleY;

    setDrawingState(prev => ({
      ...prev,
      currentAnnotation: {
        ...prev.currentAnnotation!,
        width: x - prev.startX,
        height: y - prev.startY,
      },
    }));
  }, [drawingState.isDrawing, drawingState.currentAnnotation, drawingState.startX, drawingState.startY]);

  const handleMouseUp = useCallback(() => {
    if (!drawingState.isDrawing || !drawingState.currentAnnotation) return;

    addAnnotation(drawingState.currentAnnotation as Annotation);
    setDrawingState({
      isDrawing: false,
      startX: 0,
      startY: 0,
      currentAnnotation: null,
    });
  }, [drawingState]);

  // Add annotation to list
  const addAnnotation = useCallback((annotation: Annotation) => {
    setAnnotations(prev => {
      const newAnnotations = [...prev, annotation];
      
      // Add to history
      setHistory(prevHistory => {
        const newHistory = prevHistory.slice(0, historyIndex + 1);
        newHistory.push(newAnnotations);
        return newHistory;
      });
      setHistoryIndex(prev => prev + 1);
      
      return newAnnotations;
    });
  }, [historyIndex]);

  // Undo/Redo
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(prev => prev - 1);
      setAnnotations(history[historyIndex - 1] || []);
    }
  }, [history, historyIndex]);

  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(prev => prev + 1);
      setAnnotations(history[historyIndex + 1]);
    }
  }, [history, historyIndex]);

  // Redraw when annotations change
  useEffect(() => {
    redrawCanvas();
  }, [redrawCanvas]);

  // Handle save
  const handleSave = useCallback(() => {
    onSave(annotations);
  }, [annotations, onSave]);

  if (!step.screenshot) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No screenshot available for annotation</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Tools */}
            <div className="flex items-center space-x-1">
              {tools.map((tool) => (
                <Button
                  key={tool.type}
                  onClick={() => setSelectedTool(tool.type)}
                  variant={selectedTool === tool.type ? 'primary' : 'outline'}
                  size="sm"
                  title={tool.label}
                >
                  {tool.icon}
                </Button>
              ))}
            </div>

            {/* Colors */}
            <div className="flex items-center space-x-1">
              {colors.map((color) => (
                <button
                  key={color}
                  onClick={() => setSelectedColor(color)}
                  className={`w-6 h-6 rounded border-2 ${
                    selectedColor === color ? 'border-gray-900' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>

            {/* Stroke width */}
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600">Size:</label>
              <input
                type="range"
                min="1"
                max="10"
                value={strokeWidth}
                onChange={(e) => setStrokeWidth(Number(e.target.value))}
                className="w-16"
              />
              <span className="text-sm text-gray-600 w-6">{strokeWidth}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              onClick={handleUndo}
              disabled={historyIndex <= 0}
              variant="outline"
              size="sm"
            >
              <Undo className="w-4 h-4" />
            </Button>
            <Button
              onClick={handleRedo}
              disabled={historyIndex >= history.length - 1}
              variant="outline"
              size="sm"
            >
              <Redo className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 overflow-auto bg-gray-100 p-4">
        <div className="flex justify-center">
          <canvas
            ref={canvasRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            className="border border-gray-300 bg-white cursor-crosshair max-w-full max-h-full"
            style={{ maxWidth: '100%', height: 'auto' }}
          />
        </div>
      </div>

      {/* Actions */}
      <div className="bg-gray-50 border-t border-gray-200 p-4">
        <div className="flex justify-end space-x-3">
          <Button onClick={onCancel} variant="outline">
            <X className="w-4 h-4" />
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Save className="w-4 h-4" />
            Save Annotations
          </Button>
        </div>
      </div>
    </div>
  );
};
