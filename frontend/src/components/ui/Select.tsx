/**
 * Select Component
 * Reusable select dropdown with error handling
 */

import React, { forwardRef } from 'react';
import { clsx } from 'clsx';
import { ChevronDown } from 'lucide-react';

interface SelectProps {
  className?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  children: React.ReactNode;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ className, value, onValueChange, placeholder, error, disabled, required, children, ...props }, ref) => {
    const baseClasses = [
      'block',
      'w-full',
      'px-3',
      'py-2',
      'pr-10',
      'border',
      'rounded-lg',
      'text-sm',
      'bg-white',
      'transition-colors',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'disabled:opacity-50',
      'disabled:cursor-not-allowed',
      'disabled:bg-gray-50',
      'appearance-none',
    ];

    const stateClasses = error
      ? [
          'border-red-300',
          'text-red-900',
          'focus:ring-red-500',
          'focus:border-red-500',
        ]
      : [
          'border-gray-300',
          'text-gray-900',
          'focus:ring-blue-500',
          'focus:border-blue-500',
        ];

    const classes = clsx(baseClasses, stateClasses, className);

    return (
      <div className="w-full">
        <div className="relative">
          <select
            ref={ref}
            className={classes}
            value={value}
            onChange={(e) => onValueChange?.(e.target.value)}
            disabled={disabled}
            required={required}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {children}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </div>
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';
