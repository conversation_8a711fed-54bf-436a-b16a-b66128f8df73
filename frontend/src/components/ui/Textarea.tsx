/**
 * Textarea Component
 * Reusable textarea field with error handling
 */

import React, { forwardRef } from 'react';
import { clsx } from 'clsx';

interface TextareaProps {
  className?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  rows?: number;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, placeholder, value, onChange, error, disabled, required, rows = 3, ...props }, ref) => {
    const baseClasses = [
      'block',
      'w-full',
      'px-3',
      'py-2',
      'border',
      'rounded-lg',
      'text-sm',
      'placeholder-gray-400',
      'transition-colors',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'disabled:opacity-50',
      'disabled:cursor-not-allowed',
      'disabled:bg-gray-50',
      'resize-vertical',
    ];

    const stateClasses = error
      ? [
          'border-red-300',
          'text-red-900',
          'placeholder-red-300',
          'focus:ring-red-500',
          'focus:border-red-500',
        ]
      : [
          'border-gray-300',
          'text-gray-900',
          'focus:ring-blue-500',
          'focus:border-blue-500',
        ];

    const classes = clsx(baseClasses, stateClasses, className);

    return (
      <div className="w-full">
        <textarea
          ref={ref}
          className={classes}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          disabled={disabled}
          required={required}
          rows={rows}
          {...props}
        />
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';
