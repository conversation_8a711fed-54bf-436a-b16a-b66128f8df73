/**
 * Input Component
 * Reusable input field with error handling
 */

import React, { forwardRef } from 'react';
import { clsx } from 'clsx';
import type { InputProps } from '@/types';

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, type = 'text', placeholder, value, onChange, error, disabled, required, ...props }, ref) => {
    const baseClasses = [
      'block',
      'w-full',
      'px-3',
      'py-2',
      'border',
      'rounded-lg',
      'text-sm',
      'placeholder-gray-400',
      'transition-colors',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'disabled:opacity-50',
      'disabled:cursor-not-allowed',
      'disabled:bg-gray-50',
    ];

    const stateClasses = error
      ? [
          'border-red-300',
          'text-red-900',
          'placeholder-red-300',
          'focus:ring-red-500',
          'focus:border-red-500',
        ]
      : [
          'border-gray-300',
          'text-gray-900',
          'focus:ring-blue-500',
          'focus:border-blue-500',
        ];

    const classes = clsx(baseClasses, stateClasses, className);

    return (
      <div className="w-full">
        <input
          ref={ref}
          type={type}
          className={classes}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          disabled={disabled}
          required={required}
          {...props}
        />
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';
