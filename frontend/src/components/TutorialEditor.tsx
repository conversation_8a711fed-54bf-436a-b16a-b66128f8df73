/**
 * Tutorial Editor Component
 * Advanced editor with drag-and-drop step reordering and real-time collaboration
 */

import React, { useEffect, useState, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { 
  Play, 
  Pause, 
  Plus, 
  Save, 
  Share2, 
  Download, 
  Users, 
  Eye,
  Edit3,
  Trash2,
  GripVertical,
  Image as ImageIcon,
  Type,
  MousePointer,
  Navigation
} from 'lucide-react';
import { useTutorialStore } from '@/stores/tutorialStore';
import { useAuthStore } from '@/stores/authStore';
import { websocketService } from '@/services/websocket';
import type { Step, Tutorial, Annotation } from '@/types';
import { StepEditor } from './StepEditor';
import { ImageAnnotator } from './ImageAnnotator';
import { CollaboratorsList } from './CollaboratorsList';
import { Button } from './ui/Button';
import { Modal } from './ui/Modal';
import { toast } from 'react-hot-toast';

interface TutorialEditorProps {
  tutorialId: string;
}

export const TutorialEditor: React.FC<TutorialEditorProps> = ({ tutorialId }) => {
  const { user } = useAuthStore();
  const {
    currentTutorial,
    selectedStep,
    isLoading,
    error,
    isDirty,
    isCollaborating,
    collaborators,
    fetchTutorial,
    updateTutorial,
    createStep,
    updateStep,
    deleteStep,
    reorderSteps,
    selectStep,
    joinTutorial,
    leaveTutorial,
    setDirty,
    clearError,
  } = useTutorialStore();

  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isStepEditorOpen, setIsStepEditorOpen] = useState(false);
  const [isAnnotatorOpen, setIsAnnotatorOpen] = useState(false);
  const [selectedStepForAnnotation, setSelectedStepForAnnotation] = useState<Step | null>(null);

  // Load tutorial on mount
  useEffect(() => {
    if (tutorialId) {
      fetchTutorial(tutorialId);
      joinTutorial(tutorialId);
    }

    return () => {
      if (tutorialId) {
        leaveTutorial(tutorialId);
      }
    };
  }, [tutorialId, fetchTutorial, joinTutorial, leaveTutorial]);

  // Setup WebSocket event listeners
  useEffect(() => {
    const handleStepUpdated = (data: any) => {
      if (data.tutorialId === tutorialId && data.updatedBy.id !== user?.id) {
        toast.success(`${data.updatedBy.firstName || data.updatedBy.email} updated a step`);
        // Refresh tutorial to get latest changes
        fetchTutorial(tutorialId);
      }
    };

    const handleUserJoined = (data: any) => {
      if (data.tutorialId === tutorialId && data.user.id !== user?.id) {
        toast.success(`${data.user.firstName || data.user.email} joined the tutorial`);
      }
    };

    const handleUserLeft = (data: any) => {
      if (data.tutorialId === tutorialId && data.userId !== user?.id) {
        toast.info(`User left the tutorial`);
      }
    };

    websocketService.on('step_updated', handleStepUpdated);
    websocketService.on('user_joined', handleUserJoined);
    websocketService.on('user_left', handleUserLeft);

    return () => {
      websocketService.off('step_updated', handleStepUpdated);
      websocketService.off('user_joined', handleUserJoined);
      websocketService.off('user_left', handleUserLeft);
    };
  }, [tutorialId, user?.id, fetchTutorial]);

  // Handle drag and drop reordering
  const handleDragEnd = useCallback(async (result: DropResult) => {
    if (!result.destination || !currentTutorial) return;

    const { source, destination } = result;
    if (source.index === destination.index) return;

    const steps = Array.from(currentTutorial.steps);
    const [reorderedStep] = steps.splice(source.index, 1);
    steps.splice(destination.index, 0, reorderedStep);

    // Update order numbers
    const reorderedSteps = steps.map((step, index) => ({
      id: step.id,
      order: index + 1,
    }));

    try {
      await reorderSteps(currentTutorial.id, reorderedSteps);
      toast.success('Steps reordered successfully');
    } catch (error) {
      toast.error('Failed to reorder steps');
    }
  }, [currentTutorial, reorderSteps]);

  // Handle step selection
  const handleStepSelect = useCallback((step: Step) => {
    selectStep(step);
    setIsStepEditorOpen(true);
  }, [selectStep]);

  // Handle step creation
  const handleCreateStep = useCallback(async () => {
    if (!currentTutorial) return;

    try {
      const newStep = await createStep(currentTutorial.id, {
        title: 'New Step',
        description: 'Click to edit this step',
        action: 'click',
      });
      
      selectStep(newStep);
      setIsStepEditorOpen(true);
      toast.success('Step created successfully');
    } catch (error) {
      toast.error('Failed to create step');
    }
  }, [currentTutorial, createStep, selectStep]);

  // Handle step deletion
  const handleDeleteStep = useCallback(async (stepId: string) => {
    if (!window.confirm('Are you sure you want to delete this step?')) return;

    try {
      await deleteStep(stepId);
      toast.success('Step deleted successfully');
    } catch (error) {
      toast.error('Failed to delete step');
    }
  }, [deleteStep]);

  // Handle save
  const handleSave = useCallback(async () => {
    if (!currentTutorial || !isDirty) return;

    try {
      await updateTutorial(currentTutorial.id, {
        title: currentTutorial.title,
        description: currentTutorial.description,
        tags: currentTutorial.tags,
        isPublic: currentTutorial.isPublic,
      });
      toast.success('Tutorial saved successfully');
    } catch (error) {
      toast.error('Failed to save tutorial');
    }
  }, [currentTutorial, isDirty, updateTutorial]);

  // Handle annotation
  const handleAnnotateStep = useCallback((step: Step) => {
    setSelectedStepForAnnotation(step);
    setIsAnnotatorOpen(true);
  }, []);

  // Get action icon
  const getActionIcon = (action: Step['action']) => {
    switch (action) {
      case 'click':
        return <MousePointer className="w-4 h-4" />;
      case 'type':
        return <Type className="w-4 h-4" />;
      case 'navigate':
        return <Navigation className="w-4 h-4" />;
      default:
        return <MousePointer className="w-4 h-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">{error}</p>
        <Button onClick={clearError} variant="outline" size="sm" className="mt-2">
          Dismiss
        </Button>
      </div>
    );
  }

  if (!currentTutorial) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Tutorial not found</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {currentTutorial.title}
            </h1>
            {isDirty && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Unsaved changes
              </span>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* Collaborators */}
            {isCollaborating && (
              <CollaboratorsList collaborators={collaborators} />
            )}

            {/* Action buttons */}
            <Button
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              variant={isPreviewMode ? 'primary' : 'outline'}
              size="sm"
            >
              {isPreviewMode ? <Edit3 className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {isPreviewMode ? 'Edit' : 'Preview'}
            </Button>

            <Button onClick={handleSave} disabled={!isDirty} size="sm">
              <Save className="w-4 h-4" />
              Save
            </Button>

            <Button variant="outline" size="sm">
              <Share2 className="w-4 h-4" />
              Share
            </Button>

            <Button variant="outline" size="sm">
              <Download className="w-4 h-4" />
              Export
            </Button>
          </div>
        </div>

        {/* Tutorial metadata */}
        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
          <span>{currentTutorial.steps.length} steps</span>
          <span>•</span>
          <span>{currentTutorial.viewCount} views</span>
          <span>•</span>
          <span>Last updated {new Date(currentTutorial.updatedAt).toLocaleDateString()}</span>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Steps list */}
        <div className="w-1/3 bg-gray-50 border-r border-gray-200 overflow-y-auto">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Steps</h2>
              <Button onClick={handleCreateStep} size="sm">
                <Plus className="w-4 h-4" />
                Add Step
              </Button>
            </div>

            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="steps">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {currentTutorial.steps.map((step, index) => (
                      <Draggable key={step.id} draggableId={step.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`
                              bg-white rounded-lg border border-gray-200 p-3 mb-2 cursor-pointer
                              transition-all duration-200
                              ${snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-md'}
                              ${selectedStep?.id === step.id ? 'ring-2 ring-blue-500' : ''}
                            `}
                            onClick={() => handleStepSelect(step)}
                          >
                            <div className="flex items-start space-x-3">
                              <div
                                {...provided.dragHandleProps}
                                className="mt-1 text-gray-400 hover:text-gray-600"
                              >
                                <GripVertical className="w-4 h-4" />
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">
                                    {step.order}
                                  </span>
                                  {getActionIcon(step.action)}
                                  <span className="text-xs text-gray-500 uppercase tracking-wide">
                                    {step.action}
                                  </span>
                                </div>

                                <h3 className="text-sm font-medium text-gray-900 truncate">
                                  {step.title}
                                </h3>

                                {step.description && (
                                  <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                                    {step.description}
                                  </p>
                                )}

                                {step.screenshot && (
                                  <div className="mt-2">
                                    <img
                                      src={step.screenshot}
                                      alt={step.title}
                                      className="w-full h-20 object-cover rounded border"
                                    />
                                  </div>
                                )}
                              </div>

                              <div className="flex flex-col space-y-1">
                                {step.screenshot && (
                                  <Button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAnnotateStep(step);
                                    }}
                                    variant="ghost"
                                    size="sm"
                                  >
                                    <ImageIcon className="w-3 h-3" />
                                  </Button>
                                )}

                                <Button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteStep(step.id);
                                  }}
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
        </div>

        {/* Step editor */}
        <div className="flex-1 bg-white">
          {selectedStep ? (
            <div className="h-full">
              {isPreviewMode ? (
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-4">{selectedStep.title}</h3>
                  {selectedStep.screenshot && (
                    <img
                      src={selectedStep.screenshot}
                      alt={selectedStep.title}
                      className="w-full max-w-2xl rounded-lg border shadow-sm mb-4"
                    />
                  )}
                  <p className="text-gray-700">{selectedStep.description}</p>
                </div>
              ) : (
                <StepEditor
                  step={selectedStep}
                  onUpdate={(updates) => updateStep(selectedStep.id, updates)}
                  onClose={() => setIsStepEditorOpen(false)}
                />
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <Edit3 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Select a step to edit</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Image Annotator Modal */}
      <Modal
        isOpen={isAnnotatorOpen}
        onClose={() => setIsAnnotatorOpen(false)}
        title="Annotate Screenshot"
        size="xl"
      >
        {selectedStepForAnnotation && (
          <ImageAnnotator
            step={selectedStepForAnnotation}
            onSave={(annotations) => {
              updateStep(selectedStepForAnnotation.id, { annotations });
              setIsAnnotatorOpen(false);
              toast.success('Annotations saved');
            }}
            onCancel={() => setIsAnnotatorOpen(false)}
          />
        )}
      </Modal>
    </div>
  );
};
