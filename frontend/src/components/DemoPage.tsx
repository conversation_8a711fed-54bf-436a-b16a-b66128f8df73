/**
 * Demo Page Component
 * Comprehensive demonstration of all Scribe Pro features
 */

import React, { useState } from 'react';
import { 
  Play, 
  Edit, 
  Eye, 
  Download, 
  Share2, 
  BarChart3,
  <PERSON>,
  Clock,
  CheckCircle,
  MousePointer,
  Type,
  Navigation
} from 'lucide-react';
import { TutorialEditor } from './TutorialEditor';
import { Button } from './ui/Button';
import { Modal } from './ui/Modal';
import { sampleTutorials, sampleSteps, sampleDashboardStats } from '../data/sampleTutorials';
import type { Tutorial, Step } from '../types';

export const DemoPage: React.FC = () => {
  const [selectedTutorial, setSelectedTutorial] = useState<Tutorial | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'tutorials' | 'editor' | 'analytics'>('tutorials');

  const handleEditTutorial = (tutorial: Tutorial) => {
    setSelectedTutorial(tutorial);
    setIsEditorOpen(true);
  };

  const getActionIcon = (action: Step['action']) => {
    switch (action) {
      case 'click':
        return <MousePointer className="w-4 h-4 text-blue-600" />;
      case 'type':
        return <Type className="w-4 h-4 text-green-600" />;
      case 'navigate':
        return <Navigation className="w-4 h-4 text-purple-600" />;
      default:
        return <MousePointer className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">SP</span>
                </div>
                <h1 className="text-xl font-bold text-gray-900">Scribe Pro</h1>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Demo
              </span>
            </div>

            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4" />
                Export
              </Button>
              <Button size="sm">
                <Share2 className="w-4 h-4" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
          {[
            { id: 'tutorials', label: 'Tutorials', icon: <Play className="w-4 h-4" /> },
            { id: 'editor', label: 'Editor Demo', icon: <Edit className="w-4 h-4" /> },
            { id: 'analytics', label: 'Analytics', icon: <BarChart3 className="w-4 h-4" /> },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors
                ${activeTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
                }
              `}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        {activeTab === 'tutorials' && (
          <div className="space-y-6">
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Play className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Tutorials</p>
                    <p className="text-2xl font-semibold text-gray-900">{sampleDashboardStats.totalTutorials}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Eye className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Views</p>
                    <p className="text-2xl font-semibold text-gray-900">{sampleDashboardStats.totalViews.toLocaleString()}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Completion Rate</p>
                    <p className="text-2xl font-semibold text-gray-900">{Math.round(sampleDashboardStats.avgCompletionRate * 100)}%</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Clock className="h-8 w-8 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Steps</p>
                    <p className="text-2xl font-semibold text-gray-900">{sampleDashboardStats.totalSteps}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Tutorials List */}
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Sample Tutorials</h2>
                <p className="text-sm text-gray-500 mt-1">
                  These are example tutorials showing different types of interactions and features.
                </p>
              </div>

              <div className="divide-y divide-gray-200">
                {sampleTutorials.map((tutorial) => (
                  <div key={tutorial.id} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-medium text-gray-900">{tutorial.title}</h3>
                          <span className={`
                            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            ${tutorial.isPublic ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                          `}>
                            {tutorial.isPublic ? 'Public' : 'Private'}
                          </span>
                          {tutorial.isTemplate && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Template
                            </span>
                          )}
                        </div>

                        <p className="text-gray-600 mb-3">{tutorial.description}</p>

                        <div className="flex items-center space-x-6 text-sm text-gray-500">
                          <span>{tutorial.steps.length} steps</span>
                          <span>{tutorial.viewCount.toLocaleString()} views</span>
                          <span>{tutorial.category}</span>
                          <span>Updated {new Date(tutorial.updatedAt).toLocaleDateString()}</span>
                        </div>

                        <div className="flex flex-wrap gap-2 mt-3">
                          {tutorial.tags.map((tag) => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-6">
                        <Button
                          onClick={() => handleEditTutorial(tutorial)}
                          variant="outline"
                          size="sm"
                        >
                          <Edit className="w-4 h-4" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4" />
                          Preview
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'editor' && (
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Tutorial Editor Demo</h2>
              <p className="text-sm text-gray-500 mt-1">
                Interactive demonstration of the tutorial editor with sample steps.
              </p>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Sample Steps */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 mb-4">Sample Recorded Steps</h3>
                  <div className="space-y-3">
                    {sampleSteps.map((step) => (
                      <div key={step.id} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">
                            {step.order}
                          </span>
                          {getActionIcon(step.action)}
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900">{step.title}</h4>
                            <p className="text-xs text-gray-500 mt-1">{step.description}</p>
                            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                              <span className="uppercase tracking-wide">{step.action}</span>
                              {step.timing && <span>{step.timing}s</span>}
                              {step.screenshot && <span>📷 Screenshot</span>}
                              {step.annotations.length > 0 && <span>✏️ {step.annotations.length} annotations</span>}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Features */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 mb-4">Editor Features</h3>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Edit className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Drag & Drop Reordering</h4>
                        <p className="text-sm text-gray-500">Easily reorder steps with intuitive drag and drop interface</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <Users className="w-4 h-4 text-green-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Real-time Collaboration</h4>
                        <p className="text-sm text-gray-500">Multiple users can edit simultaneously with live updates</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Edit className="w-4 h-4 text-purple-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Image Annotation</h4>
                        <p className="text-sm text-gray-500">Add arrows, highlights, and text to screenshots</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Download className="w-4 h-4 text-orange-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Multiple Export Formats</h4>
                        <p className="text-sm text-gray-500">Export as PDF, HTML, Markdown, or JSON</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <Button
                      onClick={() => handleEditTutorial(sampleTutorials[0])}
                      className="w-full"
                    >
                      <Edit className="w-4 h-4" />
                      Open Editor Demo
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Analytics Dashboard</h2>
              <p className="text-gray-600 mb-6">
                Track tutorial performance, user engagement, and completion rates.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">85%</div>
                  <div className="text-sm text-gray-500">Average Completion Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">3.2m</div>
                  <div className="text-sm text-gray-500">Average View Time</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">92%</div>
                  <div className="text-sm text-gray-500">User Satisfaction</div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-md font-medium text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {sampleDashboardStats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span className="text-gray-600">
                      {activity.type === 'tutorial_created' && 'Created tutorial'}
                      {activity.type === 'tutorial_viewed' && 'Tutorial viewed'}
                      {activity.type === 'tutorial_shared' && 'Tutorial shared'}
                    </span>
                    <span className="font-medium text-gray-900">"{activity.tutorialTitle}"</span>
                    <span className="text-gray-400">
                      {new Date(activity.timestamp).toLocaleDateString()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tutorial Editor Modal */}
      <Modal
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        title="Tutorial Editor"
        size="xl"
      >
        {selectedTutorial && (
          <div className="h-[80vh]">
            <TutorialEditor tutorialId={selectedTutorial.id} />
          </div>
        )}
      </Modal>
    </div>
  );
};
