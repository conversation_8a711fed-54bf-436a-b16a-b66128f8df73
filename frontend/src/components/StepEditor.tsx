/**
 * Step Editor Component
 * Rich editor for individual tutorial steps with real-time collaboration
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Save,
  Upload,
  Image as ImageIcon,
  Type,
  MousePointer,
  Navigation,
  Scroll,
  Hand,
  Square,
  Clock,
  X
} from 'lucide-react';
import type { Step, ActionType, UpdateStepForm } from '@/types';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { Textarea } from './ui/Textarea';
import { Select } from './ui/Select';
import { websocketService } from '@/services/websocket';
import { apiService } from '@/services/api';
import { toast } from 'react-hot-toast';

const stepSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  action: z.enum(['click', 'type', 'navigate', 'scroll', 'hover', 'select', 'submit', 'wait', 'custom']),
  element: z.string().optional(),
  elementText: z.string().optional(),
  inputValue: z.string().optional(),
  url: z.string().url('Invalid URL').optional().or(z.literal('')),
  timing: z.number().min(0).optional(),
});

type StepFormData = z.infer<typeof stepSchema>;

interface StepEditorProps {
  step: Step;
  onUpdate: (updates: UpdateStepForm) => Promise<void>;
  onClose: () => void;
}

export const StepEditor: React.FC<StepEditorProps> = ({ step, onUpdate, onClose }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [screenshotPreview, setScreenshotPreview] = useState<string | null>(step.screenshot || null);
  const [isTyping, setIsTyping] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty },
  } = useForm<StepFormData>({
    resolver: zodResolver(stepSchema),
    defaultValues: {
      title: step.title,
      description: step.description || '',
      action: step.action,
      element: step.element || '',
      elementText: step.elementText || '',
      inputValue: step.inputValue || '',
      url: step.url || '',
      timing: step.timing || 0,
    },
  });

  const watchedAction = watch('action');

  // Handle typing indicators for collaboration
  useEffect(() => {
    let typingTimeout: NodeJS.Timeout;

    const handleTypingStart = () => {
      if (!isTyping) {
        setIsTyping(true);
        websocketService.sendTypingStart(step.tutorialId, step.id, 'step-editor');
      }
      
      clearTimeout(typingTimeout);
      typingTimeout = setTimeout(() => {
        setIsTyping(false);
        websocketService.sendTypingStop(step.tutorialId, step.id, 'step-editor');
      }, 1000);
    };

    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
      input.addEventListener('input', handleTypingStart);
    });

    return () => {
      clearTimeout(typingTimeout);
      inputs.forEach(input => {
        input.removeEventListener('input', handleTypingStart);
      });
      if (isTyping) {
        websocketService.sendTypingStop(step.tutorialId, step.id, 'step-editor');
      }
    };
  }, [step.tutorialId, step.id, isTyping]);

  // Handle form submission
  const onSubmit = useCallback(async (data: StepFormData) => {
    try {
      const updates: UpdateStepForm = {
        ...data,
        screenshot: screenshotPreview || undefined,
      };

      await onUpdate(updates);
      toast.success('Step updated successfully');
    } catch (error) {
      toast.error('Failed to update step');
    }
  }, [onUpdate, screenshotPreview]);

  // Handle screenshot upload
  const handleScreenshotUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('Image must be less than 10MB');
      return;
    }

    try {
      setIsUploading(true);
      
      const result = await apiService.uploadScreenshot(file);
      setScreenshotPreview(result.imageUrl);
      
      toast.success('Screenshot uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload screenshot');
    } finally {
      setIsUploading(false);
    }
  }, []);

  // Get action icon
  const getActionIcon = (action: ActionType) => {
    switch (action) {
      case 'click':
        return <MousePointer className="w-4 h-4" />;
      case 'type':
        return <Type className="w-4 h-4" />;
      case 'navigate':
        return <Navigation className="w-4 h-4" />;
      case 'scroll':
        return <Scroll className="w-4 h-4" />;
      case 'hover':
        return <Hand className="w-4 h-4" />;
      case 'select':
        return <Square className="w-4 h-4" />;
      case 'submit':
        return <Square className="w-4 h-4" />;
      case 'wait':
        return <Clock className="w-4 h-4" />;
      default:
        return <MousePointer className="w-4 h-4" />;
    }
  };

  // Action options
  const actionOptions = [
    { value: 'click', label: 'Click', icon: <MousePointer className="w-4 h-4" /> },
    { value: 'type', label: 'Type', icon: <Type className="w-4 h-4" /> },
    { value: 'navigate', label: 'Navigate', icon: <Navigation className="w-4 h-4" /> },
    { value: 'scroll', label: 'Scroll', icon: <Scroll className="w-4 h-4" /> },
    { value: 'hover', label: 'Hover', icon: <Hand className="w-4 h-4" /> },
    { value: 'select', label: 'Select', icon: <Square className="w-4 h-4" /> },
    { value: 'submit', label: 'Submit', icon: <Square className="w-4 h-4" /> },
    { value: 'wait', label: 'Wait', icon: <Clock className="w-4 h-4" /> },
    { value: 'custom', label: 'Custom', icon: <Square className="w-4 h-4" /> },
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
              {step.order}
            </span>
            <h2 className="text-lg font-semibold text-gray-900">Edit Step</h2>
            {isTyping && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Someone is typing...
              </span>
            )}
          </div>
          <Button onClick={onClose} variant="ghost" size="sm">
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Form */}
      <div className="flex-1 overflow-y-auto p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Step Title
            </label>
            <Input
              {...register('title')}
              placeholder="Enter step title..."
              error={errors.title?.message}
            />
          </div>

          {/* Action Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Action Type
            </label>
            <Select
              value={watchedAction}
              onValueChange={(value) => setValue('action', value as ActionType)}
            >
              {actionOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  <div className="flex items-center space-x-2">
                    {option.icon}
                    <span>{option.label}</span>
                  </div>
                </option>
              ))}
            </Select>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <Textarea
              {...register('description')}
              placeholder="Describe what the user should do in this step..."
              rows={3}
              error={errors.description?.message}
            />
          </div>

          {/* Action-specific fields */}
          {(watchedAction === 'click' || watchedAction === 'hover') && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Element Selector
                </label>
                <Input
                  {...register('element')}
                  placeholder="CSS selector (e.g., #submit-button)"
                  error={errors.element?.message}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Element Text
                </label>
                <Input
                  {...register('elementText')}
                  placeholder="Text content of the element"
                  error={errors.elementText?.message}
                />
              </div>
            </>
          )}

          {watchedAction === 'type' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Input Field Selector
                </label>
                <Input
                  {...register('element')}
                  placeholder="CSS selector for input field"
                  error={errors.element?.message}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Input Value
                </label>
                <Input
                  {...register('inputValue')}
                  placeholder="Text to type"
                  error={errors.inputValue?.message}
                />
              </div>
            </>
          )}

          {watchedAction === 'navigate' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL
              </label>
              <Input
                {...register('url')}
                placeholder="https://example.com"
                error={errors.url?.message}
              />
            </div>
          )}

          {watchedAction === 'wait' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Wait Time (seconds)
              </label>
              <Input
                {...register('timing', { valueAsNumber: true })}
                type="number"
                min="0"
                step="0.1"
                placeholder="2.5"
                error={errors.timing?.message}
              />
            </div>
          )}

          {/* Screenshot */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Screenshot
            </label>
            
            {screenshotPreview ? (
              <div className="space-y-3">
                <img
                  src={screenshotPreview}
                  alt="Step screenshot"
                  className="w-full max-w-md rounded-lg border shadow-sm"
                />
                <div className="flex space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => document.getElementById('screenshot-upload')?.click()}
                    disabled={isUploading}
                  >
                    <Upload className="w-4 h-4" />
                    Replace
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setScreenshotPreview(null)}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500 mb-3">
                  Upload a screenshot for this step
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('screenshot-upload')?.click()}
                  disabled={isUploading}
                >
                  <Upload className="w-4 h-4" />
                  {isUploading ? 'Uploading...' : 'Upload Screenshot'}
                </Button>
              </div>
            )}

            <input
              id="screenshot-upload"
              type="file"
              accept="image/*"
              onChange={handleScreenshotUpload}
              className="hidden"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={!isDirty || isUploading}>
              <Save className="w-4 h-4" />
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
