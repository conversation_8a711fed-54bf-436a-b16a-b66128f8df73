/**
 * API Service for Scribe Pro Frontend
 * Handles all HTTP requests to the backend API
 */

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import type {
  ApiResponse,
  User,
  Tutorial,
  Step,
  AuthResponse,
  LoginForm,
  RegisterForm,
  CreateTutorialForm,
  UpdateTutorialForm,
  CreateStepForm,
  UpdateStepForm,
  TutorialQuery,
  PaginatedResponse,
  TutorialAnalytics,
  DashboardStats
} from '@/types';

class ApiService {
  private api: AxiosInstance;
  private accessToken: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error: AxiosError) => {
        if (error.response?.status === 401) {
          // Token expired, try to refresh
          const refreshed = await this.refreshToken();
          if (refreshed && error.config) {
            // Retry the original request
            return this.api.request(error.config);
          } else {
            // Refresh failed, redirect to login
            this.clearAuth();
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );

    // Load token from localStorage
    this.loadAuthFromStorage();
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.accessToken = token;
    localStorage.setItem('accessToken', token);
  }

  /**
   * Clear authentication
   */
  clearAuth(): void {
    this.accessToken = null;
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

  /**
   * Load auth from localStorage
   */
  private loadAuthFromStorage(): void {
    const token = localStorage.getItem('accessToken');
    if (token) {
      this.accessToken = token;
    }
  }

  /**
   * Refresh access token
   */
  private async refreshToken(): Promise<boolean> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) return false;

      const response = await axios.post(`${this.api.defaults.baseURL}/auth/refresh`, {
        refreshToken,
      });

      if (response.data.success) {
        this.setAuthToken(response.data.data.accessToken);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }

  // Authentication endpoints
  async login(credentials: LoginForm): Promise<AuthResponse> {
    const response = await this.api.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
    
    if (response.data.success && response.data.data) {
      this.setAuthToken(response.data.data.accessToken);
      localStorage.setItem('refreshToken', response.data.data.refreshToken);
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Login failed');
  }

  async register(userData: RegisterForm): Promise<AuthResponse> {
    const response = await this.api.post<ApiResponse<AuthResponse>>('/auth/register', userData);
    
    if (response.data.success && response.data.data) {
      this.setAuthToken(response.data.data.accessToken);
      localStorage.setItem('refreshToken', response.data.data.refreshToken);
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Registration failed');
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuth();
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get<ApiResponse<User>>('/auth/me');
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to get user');
  }

  // Tutorial endpoints
  async getTutorials(query: TutorialQuery = {}): Promise<PaginatedResponse<Tutorial>> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          params.append(key, value.join(','));
        } else {
          params.append(key, String(value));
        }
      }
    });

    const response = await this.api.get<ApiResponse<Tutorial[]>>(`/tutorials?${params}`);
    
    if (response.data.success && response.data.data) {
      return {
        data: response.data.data,
        pagination: response.data.pagination || {
          page: 1,
          limit: 10,
          total: response.data.data.length,
          totalPages: 1,
        },
      };
    }
    
    throw new Error(response.data.error || 'Failed to fetch tutorials');
  }

  async getTutorial(id: string): Promise<Tutorial> {
    const response = await this.api.get<ApiResponse<Tutorial>>(`/tutorials/${id}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Tutorial not found');
  }

  async createTutorial(tutorialData: CreateTutorialForm): Promise<Tutorial> {
    const response = await this.api.post<ApiResponse<Tutorial>>('/tutorials', tutorialData);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to create tutorial');
  }

  async updateTutorial(id: string, updates: UpdateTutorialForm): Promise<Tutorial> {
    const response = await this.api.put<ApiResponse<Tutorial>>(`/tutorials/${id}`, updates);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to update tutorial');
  }

  async deleteTutorial(id: string): Promise<void> {
    const response = await this.api.delete<ApiResponse>(`/tutorials/${id}`);
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete tutorial');
    }
  }

  async duplicateTutorial(id: string): Promise<Tutorial> {
    const response = await this.api.post<ApiResponse<Tutorial>>(`/tutorials/${id}/duplicate`);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to duplicate tutorial');
  }

  // Step endpoints
  async getSteps(tutorialId: string): Promise<Step[]> {
    const response = await this.api.get<ApiResponse<Step[]>>(`/steps/tutorial/${tutorialId}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to fetch steps');
  }

  async createStep(tutorialId: string, stepData: CreateStepForm): Promise<Step> {
    const response = await this.api.post<ApiResponse<Step>>(`/steps/tutorial/${tutorialId}`, stepData);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to create step');
  }

  async updateStep(id: string, updates: UpdateStepForm): Promise<Step> {
    const response = await this.api.put<ApiResponse<Step>>(`/steps/${id}`, updates);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to update step');
  }

  async deleteStep(id: string): Promise<void> {
    const response = await this.api.delete<ApiResponse>(`/steps/${id}`);
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete step');
    }
  }

  async reorderSteps(tutorialId: string, steps: Array<{ id: string; order: number }>): Promise<void> {
    const response = await this.api.post<ApiResponse>(`/steps/tutorial/${tutorialId}/reorder`, { steps });
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to reorder steps');
    }
  }

  // File upload endpoints
  async uploadScreenshot(file: File): Promise<{ imageUrl: string; width: number; height: number }> {
    const formData = new FormData();
    formData.append('screenshot', file);

    const response = await this.api.post<ApiResponse<any>>('/upload/screenshot', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to upload screenshot');
  }

  // Analytics endpoints
  async getTutorialAnalytics(id: string): Promise<TutorialAnalytics> {
    const response = await this.api.get<ApiResponse<TutorialAnalytics>>(`/analytics/tutorial/${id}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to fetch analytics');
  }

  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.api.get<ApiResponse<DashboardStats>>('/analytics/dashboard');
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.error || 'Failed to fetch dashboard stats');
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.api.get('/health');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
