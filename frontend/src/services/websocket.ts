/**
 * WebSocket Service for Real-time Collaboration
 * Handles Socket.io connection and real-time events
 */

import { io, Socket } from 'socket.io-client';
import type { WebSocketMessage, CollaborationEvent, User } from '@/types';

type EventCallback = (data: any) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventListeners = new Map<string, Set<EventCallback>>();

  /**
   * Connect to WebSocket server
   */
  connect(token: string): void {
    if (this.socket?.connected) {
      return;
    }

    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3001';

    this.socket = io(wsUrl, {
      auth: {
        token,
      },
      transports: ['websocket'],
      upgrade: true,
      rememberUpgrade: true,
    });

    this.setupEventListeners();
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  /**
   * Setup Socket.io event listeners
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('connected', { timestamp: new Date().toISOString() });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      this.emit('disconnected', { reason, timestamp: new Date().toISOString() });
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.handleReconnect();
    });

    // Tutorial collaboration events
    this.socket.on('tutorial_joined', (data) => {
      this.emit('tutorial_joined', data);
    });

    this.socket.on('user_joined', (data) => {
      this.emit('user_joined', data);
    });

    this.socket.on('user_left', (data) => {
      this.emit('user_left', data);
    });

    this.socket.on('tutorial_updated', (data) => {
      this.emit('tutorial_updated', data);
    });

    this.socket.on('step_created', (data) => {
      this.emit('step_created', data);
    });

    this.socket.on('step_updated', (data) => {
      this.emit('step_updated', data);
    });

    this.socket.on('step_deleted', (data) => {
      this.emit('step_deleted', data);
    });

    this.socket.on('cursor_moved', (data) => {
      this.emit('cursor_moved', data);
    });

    this.socket.on('typing_started', (data) => {
      this.emit('typing_started', data);
    });

    this.socket.on('typing_stopped', (data) => {
      this.emit('typing_stopped', data);
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.emit('error', error);
    });
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        this.socket?.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
      this.emit('max_reconnect_attempts_reached', {});
    }
  }

  /**
   * Join a tutorial room for collaboration
   */
  joinTutorial(tutorialId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('join_tutorial', { tutorialId });
    }
  }

  /**
   * Leave a tutorial room
   */
  leaveTutorial(tutorialId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('leave_tutorial', { tutorialId });
    }
  }

  /**
   * Send tutorial update event
   */
  sendTutorialUpdate(tutorialId: string, changes: any): void {
    if (this.socket?.connected) {
      this.socket.emit('tutorial_update', {
        tutorialId,
        changes,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Send step update event
   */
  sendStepUpdate(tutorialId: string, stepId: string, changes: any): void {
    if (this.socket?.connected) {
      this.socket.emit('step_update', {
        tutorialId,
        stepId,
        changes,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Send step creation event
   */
  sendStepCreate(tutorialId: string, step: any): void {
    if (this.socket?.connected) {
      this.socket.emit('step_create', {
        tutorialId,
        step,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Send step deletion event
   */
  sendStepDelete(tutorialId: string, stepId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('step_delete', {
        tutorialId,
        stepId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Send cursor movement event
   */
  sendCursorMove(tutorialId: string, position: { x: number; y: number }): void {
    if (this.socket?.connected) {
      this.socket.emit('cursor_move', {
        tutorialId,
        position,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Send typing start event
   */
  sendTypingStart(tutorialId: string, stepId: string, field: string): void {
    if (this.socket?.connected) {
      this.socket.emit('typing_start', {
        tutorialId,
        stepId,
        field,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Send typing stop event
   */
  sendTypingStop(tutorialId: string, stepId: string, field: string): void {
    if (this.socket?.connected) {
      this.socket.emit('typing_stop', {
        tutorialId,
        stepId,
        field,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Add event listener
   */
  on(event: string, callback: EventCallback): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback: EventCallback): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get connection status
   */
  get connected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Get socket ID
   */
  get socketId(): string | undefined {
    return this.socket?.id;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
