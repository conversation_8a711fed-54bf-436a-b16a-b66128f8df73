// Frontend Types for Scribe Pro

export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  preferences: Record<string, any>;
  timezone: string;
  language: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  isActive: boolean;
}

export interface Tutorial {
  id: string;
  title: string;
  description?: string;
  steps: Step[];
  tags: string[];
  category?: string;
  isPublic: boolean;
  isTemplate: boolean;
  settings: Record<string, any>;
  shareToken?: string;
  shareSettings: Record<string, any>;
  viewCount: number;
  analytics: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  authorId: string;
  teamId?: string;
  author?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
  team?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface Step {
  id: string;
  order: number;
  title: string;
  description?: string;
  action: ActionType;
  element?: string;
  elementText?: string;
  inputValue?: string;
  url?: string;
  screenshot?: string;
  annotations: Annotation[];
  metadata: Record<string, any>;
  timing?: number;
  tutorialId: string;
}

export type ActionType = 
  | 'click'
  | 'type'
  | 'navigate'
  | 'scroll'
  | 'hover'
  | 'select'
  | 'submit'
  | 'wait'
  | 'custom';

export interface Annotation {
  id: string;
  type: 'arrow' | 'highlight' | 'blur' | 'text' | 'circle' | 'rectangle';
  x: number;
  y: number;
  width?: number;
  height?: number;
  text?: string;
  color?: string;
  style?: Record<string, any>;
}

export interface Team {
  id: string;
  name: string;
  slug: string;
  description?: string;
  avatar?: string;
  settings: Record<string, any>;
  plan: 'free' | 'pro' | 'enterprise';
  createdAt: string;
  updatedAt: string;
}

export interface TeamMember {
  id: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: string;
  userId: string;
  teamId: string;
  user?: User;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface CreateTutorialForm {
  title: string;
  description?: string;
  tags?: string[];
  category?: string;
  isPublic?: boolean;
  teamId?: string;
}

export interface UpdateTutorialForm {
  title?: string;
  description?: string;
  tags?: string[];
  category?: string;
  isPublic?: boolean;
  settings?: Record<string, any>;
}

export interface CreateStepForm {
  title: string;
  description?: string;
  action: ActionType;
  element?: string;
  elementText?: string;
  inputValue?: string;
  url?: string;
  screenshot?: string;
  annotations?: Annotation[];
  metadata?: Record<string, any>;
  timing?: number;
}

export interface UpdateStepForm {
  title?: string;
  description?: string;
  action?: ActionType;
  element?: string;
  elementText?: string;
  inputValue?: string;
  url?: string;
  screenshot?: string;
  annotations?: Annotation[];
  metadata?: Record<string, any>;
  timing?: number;
  order?: number;
}

export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  username?: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// Query types
export interface TutorialQuery {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string[];
  category?: string;
  isPublic?: boolean;
  authorId?: string;
  teamId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
  tutorialId?: string;
}

export interface CollaborationEvent {
  type: 'step_updated' | 'step_created' | 'step_deleted' | 'tutorial_updated' | 'user_joined' | 'user_left';
  data: any;
  userId: string;
  tutorialId: string;
  timestamp: string;
  user?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
}

// UI State types
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface TutorialEditorState {
  tutorial: Tutorial | null;
  selectedStep: Step | null;
  isEditing: boolean;
  isDirty: boolean;
  collaborators: User[];
  isCollaborating: boolean;
}

export interface AnnotationTool {
  type: Annotation['type'];
  isActive: boolean;
  color: string;
  strokeWidth: number;
}

// Export formats
export interface ExportOptions {
  format: 'pdf' | 'html' | 'markdown' | 'json';
  includeScreenshots: boolean;
  includeMetadata: boolean;
  template?: string;
  branding?: {
    logo?: string;
    colors?: {
      primary: string;
      secondary: string;
    };
  };
}

// Analytics types
export interface TutorialAnalytics {
  views: number;
  uniqueViews: number;
  completions: number;
  averageTime: number;
  dropoffPoints: number[];
  demographics: Record<string, any>;
  timeline: Array<{
    date: string;
    views: number;
    completions: number;
  }>;
}

export interface DashboardStats {
  totalTutorials: number;
  totalViews: number;
  totalSteps: number;
  avgCompletionRate: number;
  recentActivity: Array<{
    id: string;
    type: 'tutorial_created' | 'tutorial_viewed' | 'tutorial_shared';
    tutorialId: string;
    tutorialTitle: string;
    timestamp: string;
  }>;
}

// Error types
export class AppError extends Error {
  code: string;
  statusCode?: number;

  constructor(message: string, code: string, statusCode?: number) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR', 400);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 'AUTHENTICATION_ERROR', 401);
  }
}

export class NetworkError extends AppError {
  constructor(message: string = 'Network error occurred') {
    super(message, 'NETWORK_ERROR', 500);
  }
}

// Utility types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps extends BaseComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}
