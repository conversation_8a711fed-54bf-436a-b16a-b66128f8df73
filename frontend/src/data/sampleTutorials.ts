/**
 * Sample Tutorial Data
 * Mock data for demonstration and testing
 */

import type { Tutorial, Step, User } from '@/types';

export const sampleUser: User = {
  id: 'user-1',
  email: '<EMAIL>',
  username: 'demo_user',
  firstName: 'Demo',
  lastName: 'User',
  avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  preferences: {
    theme: 'light',
    notifications: {
      email: true,
      push: true,
      collaboration: true,
    },
  },
  timezone: 'America/New_York',
  language: 'en',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-20T15:30:00Z',
  lastLoginAt: '2024-01-20T15:30:00Z',
  isActive: true,
};

export const sampleSteps: Step[] = [
  {
    id: 'step-1',
    order: 1,
    title: 'Click the "Sign Up" button',
    description: 'Navigate to the registration page by clicking the blue "Sign Up" button in the top right corner of the homepage.',
    action: 'click',
    element: 'button[data-testid="signup-button"]',
    elementText: 'Sign Up',
    url: 'https://example.com',
    screenshot: 'https://images.unsplash.com/photo-**********-87deedd944c3?w=800&h=600&fit=crop',
    annotations: [
      {
        id: 'annotation-1',
        type: 'arrow',
        x: 650,
        y: 50,
        width: 100,
        height: 50,
        color: '#3b82f6',
      },
      {
        id: 'annotation-2',
        type: 'highlight',
        x: 700,
        y: 40,
        width: 80,
        height: 40,
        color: '#10b981',
      },
    ],
    metadata: {
      viewport: { width: 1920, height: 1080 },
      scrollPosition: { x: 0, y: 0 },
      pageTitle: 'Welcome to Example App',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      timestamp: 1705747200000,
    },
    timing: 0,
    tutorialId: 'tutorial-1',
  },
  {
    id: 'step-2',
    order: 2,
    title: 'Enter your email address',
    description: 'Type your email address in the "Email" field. Make sure to use a valid email address that you have access to.',
    action: 'type',
    element: 'input[name="email"]',
    elementText: 'Email Address',
    inputValue: '<EMAIL>',
    url: 'https://example.com/signup',
    screenshot: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=600&fit=crop',
    annotations: [
      {
        id: 'annotation-3',
        type: 'rectangle',
        x: 300,
        y: 200,
        width: 300,
        height: 40,
        color: '#ef4444',
      },
      {
        id: 'annotation-4',
        type: 'text',
        x: 320,
        y: 190,
        text: 'Enter your email here',
        color: '#ef4444',
      },
    ],
    metadata: {
      viewport: { width: 1920, height: 1080 },
      scrollPosition: { x: 0, y: 100 },
      pageTitle: 'Sign Up - Example App',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      timestamp: 1705747260000,
    },
    timing: 2.5,
    tutorialId: 'tutorial-1',
  },
  {
    id: 'step-3',
    order: 3,
    title: 'Create a secure password',
    description: 'Enter a strong password that contains at least 8 characters, including uppercase letters, lowercase letters, numbers, and special characters.',
    action: 'type',
    element: 'input[name="password"]',
    elementText: 'Password',
    inputValue: '[HIDDEN]',
    url: 'https://example.com/signup',
    screenshot: 'https://images.unsplash.com/photo-1614064641938-3bbee52942c7?w=800&h=600&fit=crop',
    annotations: [
      {
        id: 'annotation-5',
        type: 'blur',
        x: 300,
        y: 250,
        width: 300,
        height: 40,
        color: '#6b7280',
      },
    ],
    metadata: {
      viewport: { width: 1920, height: 1080 },
      scrollPosition: { x: 0, y: 100 },
      pageTitle: 'Sign Up - Example App',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      timestamp: *************,
    },
    timing: 5.0,
    tutorialId: 'tutorial-1',
  },
  {
    id: 'step-4',
    order: 4,
    title: 'Submit the registration form',
    description: 'Click the "Create Account" button to complete your registration. You will receive a confirmation email shortly.',
    action: 'click',
    element: 'button[type="submit"]',
    elementText: 'Create Account',
    url: 'https://example.com/signup',
    screenshot: 'https://images.unsplash.com/photo-**********-87deedd944c3?w=800&h=600&fit=crop',
    annotations: [
      {
        id: 'annotation-6',
        type: 'circle',
        x: 350,
        y: 350,
        width: 200,
        height: 60,
        color: '#10b981',
      },
    ],
    metadata: {
      viewport: { width: 1920, height: 1080 },
      scrollPosition: { x: 0, y: 150 },
      pageTitle: 'Sign Up - Example App',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      timestamp: *************,
    },
    timing: 8.2,
    tutorialId: 'tutorial-1',
  },
];

export const sampleTutorials: Tutorial[] = [
  {
    id: 'tutorial-1',
    title: 'How to Create an Account',
    description: 'A comprehensive guide on how to sign up for a new account on our platform. This tutorial covers all the essential steps from finding the sign-up button to completing your registration.',
    steps: sampleSteps,
    tags: ['registration', 'getting-started', 'account'],
    category: 'User Onboarding',
    isPublic: true,
    isTemplate: false,
    settings: {
      allowComments: true,
      showStepNumbers: true,
      autoPlay: false,
      theme: 'default',
    },
    shareToken: 'abc123def456',
    shareSettings: {
      allowDownload: true,
      expiresAt: '2024-12-31T23:59:59Z',
    },
    viewCount: 1247,
    analytics: {
      completionRate: 0.85,
      averageTime: 180,
      dropoffPoints: [2, 3],
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z',
    publishedAt: '2024-01-16T09:00:00Z',
    authorId: 'user-1',
    author: {
      id: 'user-1',
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'User',
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    },
  },
  {
    id: 'tutorial-2',
    title: 'Setting Up Your Profile',
    description: 'Learn how to customize your profile with a photo, bio, and personal information to make the most of your account.',
    steps: [],
    tags: ['profile', 'customization', 'settings'],
    category: 'User Onboarding',
    isPublic: true,
    isTemplate: false,
    settings: {
      allowComments: true,
      showStepNumbers: true,
      autoPlay: false,
      theme: 'default',
    },
    shareSettings: {},
    viewCount: 892,
    analytics: {
      completionRate: 0.78,
      averageTime: 240,
      dropoffPoints: [1, 4],
    },
    createdAt: '2024-01-16T14:00:00Z',
    updatedAt: '2024-01-18T11:20:00Z',
    publishedAt: '2024-01-17T10:00:00Z',
    authorId: 'user-1',
    author: {
      id: 'user-1',
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'User',
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    },
  },
  {
    id: 'tutorial-3',
    title: 'Advanced Dashboard Features',
    description: 'Explore the powerful features of the dashboard including analytics, reporting, and data visualization tools.',
    steps: [],
    tags: ['dashboard', 'analytics', 'advanced', 'reporting'],
    category: 'Advanced Features',
    isPublic: false,
    isTemplate: true,
    settings: {
      allowComments: false,
      showStepNumbers: true,
      autoPlay: true,
      theme: 'dark',
    },
    shareSettings: {},
    viewCount: 456,
    analytics: {
      completionRate: 0.92,
      averageTime: 420,
      dropoffPoints: [6],
    },
    createdAt: '2024-01-18T09:30:00Z',
    updatedAt: '2024-01-19T16:45:00Z',
    authorId: 'user-1',
    author: {
      id: 'user-1',
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'User',
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    },
  },
];

export const sampleDashboardStats = {
  totalTutorials: 3,
  totalViews: 2595,
  totalSteps: 4,
  avgCompletionRate: 0.85,
  recentActivity: [
    {
      id: 'activity-1',
      type: 'tutorial_created' as const,
      tutorialId: 'tutorial-3',
      tutorialTitle: 'Advanced Dashboard Features',
      timestamp: '2024-01-18T09:30:00Z',
    },
    {
      id: 'activity-2',
      type: 'tutorial_viewed' as const,
      tutorialId: 'tutorial-1',
      tutorialTitle: 'How to Create an Account',
      timestamp: '2024-01-20T14:15:00Z',
    },
    {
      id: 'activity-3',
      type: 'tutorial_shared' as const,
      tutorialId: 'tutorial-2',
      tutorialTitle: 'Setting Up Your Profile',
      timestamp: '2024-01-19T11:30:00Z',
    },
  ],
};
