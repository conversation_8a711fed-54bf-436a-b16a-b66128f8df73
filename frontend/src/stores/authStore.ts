/**
 * Authentication Store
 * Manages user authentication state and actions
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { User, LoginForm, RegisterForm, AuthResponse } from '@/types';
import { apiService } from '@/services/api';
import { websocketService } from '@/services/websocket';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginForm) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginForm) => {
        try {
          set({ isLoading: true, error: null });

          const authResponse: AuthResponse = await apiService.login(credentials);
          
          set({
            user: authResponse.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Connect to WebSocket
          websocketService.connect(authResponse.accessToken);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      register: async (userData: RegisterForm) => {
        try {
          set({ isLoading: true, error: null });

          const authResponse: AuthResponse = await apiService.register(userData);
          
          set({
            user: authResponse.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Connect to WebSocket
          websocketService.connect(authResponse.accessToken);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          set({ isLoading: true });

          await apiService.logout();
          
          // Disconnect WebSocket
          websocketService.disconnect();

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

        } catch (error) {
          console.error('Logout error:', error);
          
          // Force logout even if API call fails
          websocketService.disconnect();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      getCurrentUser: async () => {
        try {
          set({ isLoading: true, error: null });

          const user = await apiService.getCurrentUser();
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Connect to WebSocket if not already connected
          const token = localStorage.getItem('accessToken');
          if (token && !websocketService.connected) {
            websocketService.connect(token);
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to get user';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          
          // Clear stored tokens on auth failure
          apiService.clearAuth();
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Auto-load user on app start if token exists
const token = localStorage.getItem('accessToken');
if (token) {
  useAuthStore.getState().getCurrentUser().catch(() => {
    // Ignore errors on initial load
  });
}
