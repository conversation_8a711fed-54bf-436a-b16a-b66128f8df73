/**
 * Tutorial Store
 * Manages tutorial state and operations
 */

import { create } from 'zustand';
import type { 
  Tutorial, 
  Step, 
  TutorialQuery, 
  CreateTutorialForm, 
  UpdateTutorialForm,
  CreateStepForm,
  UpdateStepForm,
  PaginatedResponse,
  User
} from '@/types';
import { apiService } from '@/services/api';
import { websocketService } from '@/services/websocket';

interface TutorialState {
  // State
  tutorials: Tutorial[];
  currentTutorial: Tutorial | null;
  selectedStep: Step | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  
  // Collaboration state
  collaborators: User[];
  isCollaborating: boolean;
  
  // Editor state
  isEditing: boolean;
  isDirty: boolean;

  // Actions
  fetchTutorials: (query?: TutorialQuery) => Promise<void>;
  fetchTutorial: (id: string) => Promise<void>;
  createTutorial: (data: CreateTutorialForm) => Promise<Tutorial>;
  updateTutorial: (id: string, data: UpdateTutorialForm) => Promise<void>;
  deleteTutorial: (id: string) => Promise<void>;
  duplicateTutorial: (id: string) => Promise<Tutorial>;
  
  // Step actions
  createStep: (tutorialId: string, data: CreateStepForm) => Promise<Step>;
  updateStep: (id: string, data: UpdateStepForm) => Promise<void>;
  deleteStep: (id: string) => Promise<void>;
  reorderSteps: (tutorialId: string, steps: Array<{ id: string; order: number }>) => Promise<void>;
  
  // Selection actions
  selectStep: (step: Step | null) => void;
  setCurrentTutorial: (tutorial: Tutorial | null) => void;
  
  // Collaboration actions
  joinTutorial: (tutorialId: string) => void;
  leaveTutorial: (tutorialId: string) => void;
  
  // Editor actions
  setEditing: (editing: boolean) => void;
  setDirty: (dirty: boolean) => void;
  
  // Utility actions
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useTutorialStore = create<TutorialState>((set, get) => ({
  // Initial state
  tutorials: [],
  currentTutorial: null,
  selectedStep: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  collaborators: [],
  isCollaborating: false,
  isEditing: false,
  isDirty: false,

  // Tutorial actions
  fetchTutorials: async (query: TutorialQuery = {}) => {
    try {
      set({ isLoading: true, error: null });

      const response: PaginatedResponse<Tutorial> = await apiService.getTutorials(query);
      
      set({
        tutorials: response.data,
        pagination: response.pagination,
        isLoading: false,
        error: null,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tutorials';
      set({
        isLoading: false,
        error: errorMessage,
      });
    }
  },

  fetchTutorial: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      const tutorial = await apiService.getTutorial(id);
      
      set({
        currentTutorial: tutorial,
        selectedStep: null,
        isLoading: false,
        error: null,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tutorial';
      set({
        currentTutorial: null,
        selectedStep: null,
        isLoading: false,
        error: errorMessage,
      });
    }
  },

  createTutorial: async (data: CreateTutorialForm) => {
    try {
      set({ isLoading: true, error: null });

      const tutorial = await apiService.createTutorial(data);
      
      set((state) => ({
        tutorials: [tutorial, ...state.tutorials],
        currentTutorial: tutorial,
        isLoading: false,
        error: null,
      }));

      return tutorial;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create tutorial';
      set({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  updateTutorial: async (id: string, data: UpdateTutorialForm) => {
    try {
      set({ isLoading: true, error: null });

      const updatedTutorial = await apiService.updateTutorial(id, data);
      
      set((state) => ({
        tutorials: state.tutorials.map(t => t.id === id ? updatedTutorial : t),
        currentTutorial: state.currentTutorial?.id === id ? updatedTutorial : state.currentTutorial,
        isLoading: false,
        error: null,
        isDirty: false,
      }));

      // Send collaboration event
      if (get().isCollaborating) {
        websocketService.sendTutorialUpdate(id, data);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update tutorial';
      set({
        isLoading: false,
        error: errorMessage,
      });
    }
  },

  deleteTutorial: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      await apiService.deleteTutorial(id);
      
      set((state) => ({
        tutorials: state.tutorials.filter(t => t.id !== id),
        currentTutorial: state.currentTutorial?.id === id ? null : state.currentTutorial,
        selectedStep: null,
        isLoading: false,
        error: null,
      }));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete tutorial';
      set({
        isLoading: false,
        error: errorMessage,
      });
    }
  },

  duplicateTutorial: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      const duplicatedTutorial = await apiService.duplicateTutorial(id);
      
      set((state) => ({
        tutorials: [duplicatedTutorial, ...state.tutorials],
        isLoading: false,
        error: null,
      }));

      return duplicatedTutorial;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to duplicate tutorial';
      set({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  // Step actions
  createStep: async (tutorialId: string, data: CreateStepForm) => {
    try {
      const step = await apiService.createStep(tutorialId, data);
      
      set((state) => {
        if (state.currentTutorial?.id === tutorialId) {
          const updatedTutorial = {
            ...state.currentTutorial,
            steps: [...state.currentTutorial.steps, step].sort((a, b) => a.order - b.order),
          };
          
          return {
            currentTutorial: updatedTutorial,
            selectedStep: step,
            isDirty: true,
          };
        }
        return state;
      });

      // Send collaboration event
      if (get().isCollaborating) {
        websocketService.sendStepCreate(tutorialId, step);
      }

      return step;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create step';
      set({ error: errorMessage });
      throw error;
    }
  },

  updateStep: async (id: string, data: UpdateStepForm) => {
    try {
      const updatedStep = await apiService.updateStep(id, data);
      
      set((state) => {
        if (state.currentTutorial) {
          const updatedTutorial = {
            ...state.currentTutorial,
            steps: state.currentTutorial.steps.map(s => s.id === id ? updatedStep : s),
          };
          
          return {
            currentTutorial: updatedTutorial,
            selectedStep: state.selectedStep?.id === id ? updatedStep : state.selectedStep,
            isDirty: true,
          };
        }
        return state;
      });

      // Send collaboration event
      if (get().isCollaborating && get().currentTutorial) {
        websocketService.sendStepUpdate(get().currentTutorial!.id, id, data);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update step';
      set({ error: errorMessage });
    }
  },

  deleteStep: async (id: string) => {
    try {
      await apiService.deleteStep(id);
      
      set((state) => {
        if (state.currentTutorial) {
          const updatedTutorial = {
            ...state.currentTutorial,
            steps: state.currentTutorial.steps.filter(s => s.id !== id),
          };
          
          return {
            currentTutorial: updatedTutorial,
            selectedStep: state.selectedStep?.id === id ? null : state.selectedStep,
            isDirty: true,
          };
        }
        return state;
      });

      // Send collaboration event
      if (get().isCollaborating && get().currentTutorial) {
        websocketService.sendStepDelete(get().currentTutorial!.id, id);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete step';
      set({ error: errorMessage });
    }
  },

  reorderSteps: async (tutorialId: string, steps: Array<{ id: string; order: number }>) => {
    try {
      await apiService.reorderSteps(tutorialId, steps);
      
      // Update local state
      set((state) => {
        if (state.currentTutorial?.id === tutorialId) {
          const stepMap = new Map(state.currentTutorial.steps.map(s => [s.id, s]));
          const reorderedSteps = steps
            .map(({ id, order }) => ({ ...stepMap.get(id)!, order }))
            .sort((a, b) => a.order - b.order);
          
          return {
            currentTutorial: {
              ...state.currentTutorial,
              steps: reorderedSteps,
            },
            isDirty: true,
          };
        }
        return state;
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reorder steps';
      set({ error: errorMessage });
    }
  },

  // Selection actions
  selectStep: (step: Step | null) => {
    set({ selectedStep: step });
  },

  setCurrentTutorial: (tutorial: Tutorial | null) => {
    set({ 
      currentTutorial: tutorial,
      selectedStep: null,
      isDirty: false,
    });
  },

  // Collaboration actions
  joinTutorial: (tutorialId: string) => {
    websocketService.joinTutorial(tutorialId);
    set({ isCollaborating: true });
  },

  leaveTutorial: (tutorialId: string) => {
    websocketService.leaveTutorial(tutorialId);
    set({ 
      isCollaborating: false,
      collaborators: [],
    });
  },

  // Editor actions
  setEditing: (editing: boolean) => {
    set({ isEditing: editing });
  },

  setDirty: (dirty: boolean) => {
    set({ isDirty: dirty });
  },

  // Utility actions
  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));
