{"name": "scribe-pro-frontend", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^4.40.1", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-markdown": "^8.0.7", "react-router-dom": "^6.15.0", "react-syntax-highlighter": "^15.5.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^1.14.0", "uuid": "^11.1.0", "zod": "^3.25.76", "zustand": "^4.5.7"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@types/react": "^18.2.15", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/ui": "^0.34.0", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "^5.3.0", "vite": "^4.4.5", "vitest": "^0.34.0"}}