{"name": "scribe-pro-frontend", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "lucide-react": "^0.263.1", "@tanstack/react-query": "^4.35.0", "react-hook-form": "^7.45.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.4", "socket.io-client": "^4.7.4", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "react-beautiful-dnd": "^13.1.1", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "date-fns": "^2.30.0", "zustand": "^4.4.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-syntax-highlighter": "^15.5.7", "@vitejs/plugin-react": "^4.0.3", "typescript": "^5.3.0", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "vitest": "^0.34.0", "@vitest/ui": "^0.34.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0"}}