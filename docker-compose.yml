version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: scribe-pro-postgres
    environment:
      POSTGRES_DB: scribe_pro
      POSTGRES_USER: scribe_user
      POSTGRES_PASSWORD: scribe_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - scribe-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: scribe-pro-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - scribe-network

  # MinIO (S3-compatible storage)
  minio:
    image: minio/minio:latest
    container_name: scribe-pro-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - scribe-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: scribe-pro-backend
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ******************************************************/scribe_pro
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-in-production
      AWS_ACCESS_KEY_ID: minioadmin
      AWS_SECRET_ACCESS_KEY: minioadmin123
      AWS_ENDPOINT: http://minio:9000
      AWS_BUCKET_NAME: scribe-pro-uploads
      AWS_REGION: us-east-1
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - minio
    networks:
      - scribe-network

  # Frontend Web App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: scribe-pro-frontend
    environment:
      VITE_API_URL: http://localhost:3001/api
      VITE_WS_URL: ws://localhost:3001
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - scribe-network

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  scribe-network:
    driver: bridge
