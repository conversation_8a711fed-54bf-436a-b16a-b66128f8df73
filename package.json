{"name": "scribe-alternative", "version": "1.0.0", "description": "A fullstack web app for recording user interactions and creating step-by-step tutorials", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["tutorial", "recording", "chrome-extension", "scribe"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}