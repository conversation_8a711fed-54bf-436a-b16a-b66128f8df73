{"name": "scribe-pro", "version": "2.0.0", "description": "Advanced tutorial recording platform with intelligent interaction capture and professional export capabilities", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:extension\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:extension": "cd extension && npm run dev", "build": "npm run build:backend && npm run build:frontend && npm run build:extension", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:extension": "cd extension && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../extension && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend && npm run lint:extension", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:extension": "cd extension && npm run lint", "type-check": "npm run type-check:backend && npm run type-check:frontend && npm run type-check:extension", "type-check:backend": "cd backend && npm run type-check", "type-check:frontend": "cd frontend && npm run type-check", "type-check:extension": "cd extension && npm run type-check", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "start": "cd backend && npm start"}, "keywords": ["tutorial", "recording", "chrome-extension", "scribe", "typescript", "react", "nodejs", "postgresql"], "author": "Scribe Pro Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["backend", "frontend", "extension"]}