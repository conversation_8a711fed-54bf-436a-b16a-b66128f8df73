/* Scribe Pro Extension Popup Styles */
body {
    width: 380px;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: #ffffff;
    color: #1f2937;
}

.popup-container {
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.icon-button {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 6px;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;
}

.icon-button:hover {
    background: #f3f4f6;
    color: #374151;
}

/* Status Section */
.status-section {
    margin-bottom: 20px;
}

.status {
    padding: 12px 16px;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.status.ready {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.status.recording {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.status.paused {
    background-color: #fffbeb;
    border: 1px solid #fed7aa;
    color: #d97706;
}

.step-count {
    text-align: center;
    font-size: 12px;
    color: #6b7280;
}

/* Current Tutorial */
.current-tutorial {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 20px;
}

.current-tutorial h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.current-tutorial p {
    margin: 4px 0;
    font-size: 12px;
    color: #6b7280;
}

/* Controls Section */
.controls-section {
    margin-bottom: 20px;
}

.input-group {
    margin-bottom: 16px;
}

.input-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
}

.input-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.2s;
}

.input-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Buttons */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2563eb;
}

.btn-danger {
    background: #dc2626;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #b91c1c;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #4b5563;
}

.btn-outline {
    background: transparent;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-outline:hover:not(:disabled) {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Settings Panel */
.settings-panel {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.settings-panel h3 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.option-group {
    margin-bottom: 16px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 13px;
    color: #374151;
    margin-bottom: 4px;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.option-group small {
    display: block;
    font-size: 11px;
    color: #6b7280;
    margin-left: 24px;
}

.slider {
    width: 100%;
    margin: 8px 0;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 20px;
}

/* Message */
.message {
    padding: 10px 12px;
    border-radius: 6px;
    font-size: 13px;
    margin-bottom: 16px;
}

.message.success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.message.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

/* Footer */
.footer {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.footer small {
    color: #6b7280;
    font-size: 11px;
}

/* Content Script Styles (injected into pages) */
.scribe-recording-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    user-select: none;
    pointer-events: none;
}

.scribe-indicator-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.scribe-recording-dot {
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    animation: scribe-pulse 1.5s infinite;
}

.scribe-recording-text {
    color: #ef4444;
    font-weight: 600;
}

.scribe-step-counter {
    color: #94a3b8;
    font-size: 12px;
}

@keyframes scribe-pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.scribe-recording-indicator.paused .scribe-recording-dot {
    background: #f59e0b;
    animation: none;
}

.scribe-recording-indicator.paused .scribe-recording-text {
    color: #f59e0b;
}
