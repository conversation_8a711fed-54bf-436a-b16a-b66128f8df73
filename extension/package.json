{"name": "scribe-pro-extension", "version": "2.0.0", "description": "Advanced Chrome extension for intelligent tutorial recording", "main": "dist/background.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"uuid": "^9.0.1"}, "devDependencies": {"@types/chrome": "^0.0.246", "@types/uuid": "^9.0.7", "typescript": "^5.3.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "ts-loader": "^9.5.0", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}}