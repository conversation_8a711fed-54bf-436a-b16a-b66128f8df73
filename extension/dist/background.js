(()=>{"use strict";const e={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let t;const r=new Uint8Array(16);function n(){if(!t&&(t="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!t))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return t(r)}const o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));const a=function(t,r,a){if(e.randomUUID&&!r&&!t)return e.randomUUID();const i=(t=t||{}).random||(t.rng||n)();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,r){a=a||0;for(let e=0;e<16;++e)r[a+e]=i[e];return r}return function(e,t=0){return o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]}(i)};let i={isRecording:!1,currentTutorialId:null,stepCount:0,startTime:null},s=[],c=null,l={captureScreenshots:!0,captureScrolling:!0,captureHovers:!1,captureFormInputs:!0,captureNavigation:!0,autoGenerateDescriptions:!0,highlightElements:!0,blurSensitiveData:!0,screenshotQuality:.9};const u="http://localhost:3001/api";let d=null;async function p(){try{await chrome.storage.local.set({recordingState:i,authToken:d,recordingOptions:l,currentTutorial:c,recordedSteps:s.slice(-50)})}catch(e){console.error("Failed to save state:",e)}}async function h(){try{if(!i.isRecording||!c)throw new Error("No recording in progress");s.length>0&&await async function(e,t){for(const r of t)try{await fetch(`${u}/steps/tutorial/${e}`,{method:"POST",headers:{"Content-Type":"application/json",...d&&{Authorization:`Bearer ${d}`}},body:JSON.stringify({title:r.title,description:r.description,action:r.action,element:r.element,elementText:r.elementText,inputValue:r.inputValue,url:r.url,screenshot:r.screenshot,annotations:r.annotations,metadata:r.metadata,timing:r.timing})})}catch(e){console.error("Failed to save step:",r.id,e)}}(c.id,s);const e=await async function(e,t){const r=await fetch(`${u}/tutorials/${e}`,{method:"PUT",headers:{"Content-Type":"application/json",...d&&{Authorization:`Bearer ${d}`}},body:JSON.stringify(t)}),n=await r.json();if(!n.success||!n.data)throw new Error(n.error||"Failed to update tutorial");return n.data}(c.id,{steps:s,updatedAt:(new Date).toISOString()}),t=c;return i={isRecording:!1,currentTutorialId:null,stepCount:0,startTime:null},c=null,await chrome.action.setBadgeText({text:""}),await T("RECORDING_STOPPED",{tutorialId:t.id}),await chrome.tabs.create({url:`http://localhost:5173/tutorial/${t.id}/edit`}),await p(),console.log("Recording stopped:",t.id,"Steps:",s.length),{success:!0,tutorial:e}}catch(e){return console.error("Failed to stop recording:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function g(e,t){try{if(!i.isRecording||!c)throw new Error("No recording in progress");const r={id:a(),order:s.length+1,title:e.title||w(e),description:e.description||y(e),action:e.action||"click",element:e.element||"",elementText:e.elementText,inputValue:e.inputValue,url:t?.url||e.url||"",screenshot:e.screenshot,annotations:e.annotations||[],metadata:e.metadata||{viewport:{width:0,height:0},scrollPosition:{x:0,y:0},pageTitle:t?.title||"",userAgent:navigator.userAgent,timestamp:Date.now()},timing:e.timing||0,timestamp:(new Date).toISOString(),tutorialId:c.id};return s.push(r),i.stepCount=s.length,l.captureScreenshots&&t?.id&&setTimeout(()=>{t.id&&m(t.id,{format:"png",quality:l.screenshotQuality})},500),await p(),console.log("Step recorded:",r.id,r.title),{success:!0,step:r}}catch(e){return console.error("Failed to record step:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function m(e,t){try{const e=await chrome.tabs.captureVisibleTab({format:t?.format||"png",quality:t?.quality||l.screenshotQuality}),r=await async function(e){try{const t=await fetch(e),r=await t.blob(),n=new FormData;n.append("screenshot",r,`screenshot-${Date.now()}.png`);const o=await fetch(`${u}/upload/screenshot`,{method:"POST",headers:{...d&&{Authorization:`Bearer ${d}`}},body:n});return await o.json()}catch(e){return console.error("Failed to upload screenshot:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}(e);if(r.success&&r.data)return s.length>0&&(s[s.length-1].screenshot=r.data.imageUrl,await p()),{success:!0,imageUrl:r.data.imageUrl};throw new Error(r.error||"Failed to upload screenshot")}catch(e){return console.error("Failed to take screenshot:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}function w(e){const t=e.action||"interact",r=e.elementText||"",n=e.element||"";switch(t){case"click":return r?`Click "${r}"`:n.includes("button")?"Click button":n.includes("link")||n.includes("a[")?"Click link":"Click element";case"type":return n.includes('input[type="email"]')||n.includes("email")?"Enter email address":n.includes('input[type="password"]')||n.includes("password")?"Enter password":n.includes("textarea")?"Enter text":"Type in field";case"select":return r?`Select "${r}"`:"Select option";case"navigate":return"Navigate to page";case"scroll":return"Scroll page";case"hover":return r?`Hover over "${r}"`:"Hover over element";default:return`${t.charAt(0).toUpperCase()+t.slice(1)} element`}}function y(e){const t=e.action||"interact",r=e.elementText||"",n=e.element||"",o=e.inputValue||"";switch(t){case"click":return r?`Click the "${r}" ${f(n)}.`:`Click the ${f(n)}.`;case"type":return o&&(a=n,!["password","ssn","credit","card","cvv","security"].some(e=>a.toLowerCase().includes(e)))?`Enter "${o}" in the ${E(n)} field.`:`Enter text in the ${E(n)} field.`;case"select":return`Select "${r}" from the dropdown menu.`;case"navigate":return"Navigate to the page.";case"scroll":return"Scroll down the page to view more content.";case"hover":return`Hover over the ${r||"element"} to reveal additional options.`;default:return`Perform ${t} action on the element.`}var a}function f(e){return e.includes("button")||e.includes('[type="button"]')||e.includes('[type="submit"]')?"button":e.includes("a[")||e.includes("link")?"link":e.includes("input")?"input field":e.includes("select")?"dropdown":e.includes("textarea")?"text area":e.includes("img")?"image":"element"}function E(e){return e.includes("email")?"email":e.includes("password")?"password":e.includes("name")?"name":e.includes("phone")?"phone":e.includes("address")?"address":e.includes("search")?"search":"input"}async function T(e,t){try{const r=await chrome.tabs.query({}),n={type:e,payload:t,timestamp:Date.now()};for(const e of r)e.id&&chrome.tabs.sendMessage(e.id,n).catch(()=>{})}catch(e){console.error("Failed to notify tabs:",e)}}chrome.runtime.onInstalled.addListener(async()=>{console.log("Scribe Pro extension installed"),await async function(){try{const e=await chrome.storage.local.get(["recordingState","authToken","recordingOptions"]);e.recordingState&&(i=e.recordingState),e.authToken&&(d=e.authToken),e.recordingOptions&&(l={...l,...e.recordingOptions})}catch(e){console.error("Failed to load stored state:",e)}}(),await async function(){try{await fetch(`${u}/analytics/track`,{method:"POST",headers:{"Content-Type":"application/json",...d&&{Authorization:`Bearer ${d}`}},body:JSON.stringify({event:"extension_started",properties:{version:chrome.runtime.getManifest().version,userAgent:navigator.userAgent,timestamp:Date.now()}})}).catch(()=>{})}catch(e){console.error("Failed to initialize analytics:",e)}}()}),chrome.runtime.onMessage.addListener((e,t,r)=>(async function(e,t){switch(e.type){case"START_RECORDING":return await async function(e="New Tutorial",t){try{if(i.isRecording)throw new Error("Recording is already in progress");t&&(l={...l,...t});const r=await async function(e){const t=await fetch(`${u}/tutorials`,{method:"POST",headers:{"Content-Type":"application/json",...d&&{Authorization:`Bearer ${d}`}},body:JSON.stringify({title:e,description:`Tutorial recorded on ${(new Date).toLocaleDateString()}`,isPublic:!1})}),r=await t.json();if(!r.success||!r.data)throw new Error(r.error||"Failed to create tutorial");return r.data}(e);return i={isRecording:!0,currentTutorialId:r.id,stepCount:0,startTime:Date.now()},c=r,s=[],await chrome.action.setBadgeText({text:"REC"}),await chrome.action.setBadgeBackgroundColor({color:"#dc2626"}),await T("RECORDING_STARTED",{tutorialId:r.id}),await p(),console.log("Recording started:",r.id),{success:!0,tutorial:r}}catch(e){return console.error("Failed to start recording:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}(e.payload?.tutorialTitle,e.payload?.options);case"STOP_RECORDING":return await h();case"PAUSE_RECORDING":return await async function(){if(!i.isRecording)throw new Error("No recording in progress");return await T("RECORDING_PAUSED",{}),await chrome.action.setBadgeText({text:"PAUSE"}),await chrome.action.setBadgeBackgroundColor({color:"#f59e0b"}),{success:!0}}();case"RESUME_RECORDING":return await async function(){if(!i.isRecording)throw new Error("No recording in progress");return await T("RECORDING_RESUMED",{}),await chrome.action.setBadgeText({text:"REC"}),await chrome.action.setBadgeBackgroundColor({color:"#dc2626"}),{success:!0}}();case"GET_RECORDING_STATUS":return{...i,tutorial:c||void 0,options:l};case"RECORD_STEP":return await g(e.payload?.step,t.tab);case"TAKE_SCREENSHOT":return t.tab?.id?await m(t.tab.id,e.payload?.options):{success:!1,error:"No tab ID available"};case"UPDATE_STEP":return await async function(e,t){try{const r=s.findIndex(t=>t.id===e);if(-1===r)throw new Error("Step not found");return s[r]={...s[r],...t},await p(),{success:!0,step:s[r]}}catch(e){return console.error("Failed to update step:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}(e.payload?.stepId,e.payload?.updates);case"DELETE_STEP":return await async function(e){try{const t=s.findIndex(t=>t.id===e);if(-1===t)throw new Error("Step not found");return s.splice(t,1),s.forEach((e,t)=>{e.order=t+1}),i.stepCount=s.length,await p(),{success:!0}}catch(e){return console.error("Failed to delete step:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}(e.payload?.stepId);default:throw new Error(`Unknown message type: ${e.type}`)}}(e,t).then(e=>r(e)).catch(e=>{console.error("Message handling error:",e),r({success:!1,error:e.message})}),!0)),chrome.tabs.onUpdated.addListener(async(e,t,r)=>{i.isRecording&&"complete"===t.status&&r.url&&await g({action:"navigate",url:r.url,title:`Navigate to ${r.title||r.url}`,description:`Navigate to ${r.url}`,metadata:{viewport:{width:0,height:0},scrollPosition:{x:0,y:0},pageTitle:r.title||"",userAgent:navigator.userAgent,timestamp:Date.now()}},r)}),chrome.action.onClicked.addListener(async e=>{i.isRecording?await h():chrome.action.openPopup()}),console.log("Scribe Pro background service worker loaded")})();