(()=>{"use strict";let e,t,n,o,r,s,a,i,c={isRecording:!1,currentTutorialId:null,stepCount:0,startTime:null};async function l(){try{const e=await chrome.runtime.sendMessage({type:"GET_RECORDING_STATUS",timestamp:Date.now()});e&&(c=e)}catch(e){console.error("Failed to load current state:",e)}}function d(){if(c.isRecording){if(e.style.display="none",t.style.display="inline-block",n.style.display="inline-block",n.textContent="Pause",o.textContent="Recording in progress...",o.className="status recording",s.textContent=`Steps recorded: ${c.stepCount}`,s.style.display="block",r.disabled=!0,c.tutorial){const e=document.getElementById("currentTutorial");e&&(e.innerHTML=`\n          <h3>Current Tutorial</h3>\n          <p><strong>${c.tutorial.title}</strong></p>\n          <p>Started: ${new Date(c.tutorial.createdAt).toLocaleTimeString()}</p>\n        `,e.style.display="block")}}else{e.style.display="inline-block",t.style.display="none",n.style.display="none",o.textContent="Ready to record",o.className="status ready",s.style.display="none",r.disabled=!1;const a=document.getElementById("currentTutorial");a&&(a.style.display="none")}c.options&&function(e){const t=a;t.querySelector('[name="captureScreenshots"]').checked=e.captureScreenshots,t.querySelector('[name="captureScrolling"]').checked=e.captureScrolling,t.querySelector('[name="captureHovers"]').checked=e.captureHovers,t.querySelector('[name="captureFormInputs"]').checked=e.captureFormInputs,t.querySelector('[name="captureNavigation"]').checked=e.captureNavigation,t.querySelector('[name="autoGenerateDescriptions"]').checked=e.autoGenerateDescriptions,t.querySelector('[name="highlightElements"]').checked=e.highlightElements,t.querySelector('[name="blurSensitiveData"]').checked=e.blurSensitiveData,t.querySelector('[name="screenshotQuality"]').value=e.screenshotQuality.toString()}(c.options)}async function u(){const t=r.value.trim();if(t)try{e.disabled=!0,e.textContent="Starting...";const n=y(),o=await chrome.runtime.sendMessage({type:"START_RECORDING",payload:{tutorialTitle:t,options:n},timestamp:Date.now()});if(!o.success)throw new Error(o.error||"Failed to start recording");c.isRecording=!0,c.tutorial=o.tutorial,c.stepCount=0,d(),E("Recording started!")}catch(e){console.error("Failed to start recording:",e),S(e instanceof Error?e.message:"Failed to start recording")}finally{e.disabled=!1,e.textContent="Start Recording"}else S("Please enter a tutorial title")}async function p(){try{t.disabled=!0,t.textContent="Stopping...";const e=await chrome.runtime.sendMessage({type:"STOP_RECORDING",timestamp:Date.now()});if(!e.success)throw new Error(e.error||"Failed to stop recording");c.isRecording=!1,c.tutorial=void 0,c.stepCount=0,d(),E(`Recording stopped! ${e.tutorial?.steps?.length||0} steps recorded.`),r.value=""}catch(e){console.error("Failed to stop recording:",e),S(e instanceof Error?e.message:"Failed to stop recording")}finally{t.disabled=!1,t.textContent="Stop Recording"}}async function m(){try{const e="Resume"===n.textContent,t=e?"RESUME_RECORDING":"PAUSE_RECORDING";n.disabled=!0;const r=await chrome.runtime.sendMessage({type:t,timestamp:Date.now()});if(!r.success)throw new Error(r.error||"Failed to pause/resume recording");n.textContent=e?"Pause":"Resume",o.textContent=e?"Recording in progress...":"Recording paused",o.className=e?"status recording":"status paused"}catch(e){console.error("Failed to pause/resume recording:",e),S(e instanceof Error?e.message:"Failed to pause/resume recording")}finally{n.disabled=!1}}function g(){const e=y();chrome.storage.local.set({recordingOptions:e})}function y(){const e=new FormData(a);return{captureScreenshots:"on"===e.get("captureScreenshots"),captureScrolling:"on"===e.get("captureScrolling"),captureHovers:"on"===e.get("captureHovers"),captureFormInputs:"on"===e.get("captureFormInputs"),captureNavigation:"on"===e.get("captureNavigation"),autoGenerateDescriptions:"on"===e.get("autoGenerateDescriptions"),highlightElements:"on"===e.get("highlightElements"),blurSensitiveData:"on"===e.get("blurSensitiveData"),screenshotQuality:parseFloat(e.get("screenshotQuality"))||.9}}function h(){const e=document.getElementById("settingsPanel");e&&(e.style.display="none"===e.style.display?"block":"none")}function E(e){v(e,"success")}function S(e){v(e,"error")}function v(e,t){const n=document.getElementById("message");n&&(n.textContent=e,n.className=`message ${t}`,n.style.display="block",setTimeout(()=>{n.style.display="none"},3e3))}document.addEventListener("DOMContentLoaded",async()=>{e=document.getElementById("startButton"),t=document.getElementById("stopButton"),n=document.getElementById("pauseButton"),o=document.getElementById("status"),r=document.getElementById("tutorialTitle"),s=document.getElementById("stepCount"),a=document.getElementById("optionsForm"),i=document.getElementById("tutorialsList"),function(){e.addEventListener("click",u),t.addEventListener("click",p),n.addEventListener("click",m),a.addEventListener("change",g),r.addEventListener("input",()=>{e.disabled=!r.value.trim()});const o=document.getElementById("openApp");o&&o.addEventListener("click",()=>{chrome.tabs.create({url:"http://localhost:5173"}),window.close()});const s=document.getElementById("settings");s&&s.addEventListener("click",h)}(),await l(),d()}),setInterval(async()=>{await l(),d()},2e3)})();