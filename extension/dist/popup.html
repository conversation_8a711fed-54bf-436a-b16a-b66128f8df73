<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Scribe Pro</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="popup-container">
    <!-- Header -->
    <div class="header">
      <div class="logo">
        <img src="icon-48.png" alt="Scribe Pro" width="24" height="24">
        <h1>Scribe Pro</h1>
      </div>
      <button id="settings" class="icon-button" title="Settings">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"></circle>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
        </svg>
      </button>
    </div>

    <!-- Status -->
    <div class="status-section">
      <div id="status" class="status ready">Ready to record</div>
      <div id="stepCount" class="step-count" style="display: none;">Steps recorded: 0</div>
    </div>

    <!-- Current Tutorial Info -->
    <div id="currentTutorial" class="current-tutorial" style="display: none;">
      <!-- Populated by JavaScript -->
    </div>

    <!-- Recording Controls -->
    <div class="controls-section">
      <div class="input-group">
        <label for="tutorialTitle">Tutorial Title</label>
        <input 
          type="text" 
          id="tutorialTitle" 
          placeholder="Enter tutorial title..."
          maxlength="100"
        >
      </div>

      <div class="button-group">
        <button id="startButton" class="btn btn-primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <polygon points="10,8 16,12 10,16"></polygon>
          </svg>
          Start Recording
        </button>
        
        <button id="stopButton" class="btn btn-danger" style="display: none;">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="6" y="6" width="12" height="12"></rect>
          </svg>
          Stop Recording
        </button>
        
        <button id="pauseButton" class="btn btn-secondary" style="display: none;">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Pause
        </button>
      </div>
    </div>

    <!-- Settings Panel -->
    <div id="settingsPanel" class="settings-panel" style="display: none;">
      <h3>Recording Options</h3>
      <form id="optionsForm">
        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="captureScreenshots" checked>
            <span class="checkmark"></span>
            Capture Screenshots
          </label>
          <small>Automatically take screenshots for each step</small>
        </div>

        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="captureFormInputs" checked>
            <span class="checkmark"></span>
            Capture Form Inputs
          </label>
          <small>Record text input and form interactions</small>
        </div>

        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="captureNavigation" checked>
            <span class="checkmark"></span>
            Capture Navigation
          </label>
          <small>Record page navigation and URL changes</small>
        </div>

        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="captureScrolling" checked>
            <span class="checkmark"></span>
            Capture Scrolling
          </label>
          <small>Record scroll actions</small>
        </div>

        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="captureHovers">
            <span class="checkmark"></span>
            Capture Hover Events
          </label>
          <small>Record mouse hover interactions</small>
        </div>

        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="highlightElements" checked>
            <span class="checkmark"></span>
            Highlight Elements
          </label>
          <small>Visually highlight clicked elements</small>
        </div>

        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="autoGenerateDescriptions" checked>
            <span class="checkmark"></span>
            Auto-Generate Descriptions
          </label>
          <small>Automatically create step descriptions</small>
        </div>

        <div class="option-group">
          <label class="checkbox-label">
            <input type="checkbox" name="blurSensitiveData" checked>
            <span class="checkmark"></span>
            Blur Sensitive Data
          </label>
          <small>Hide passwords and sensitive information</small>
        </div>

        <div class="option-group">
          <label for="screenshotQuality">Screenshot Quality</label>
          <input 
            type="range" 
            name="screenshotQuality" 
            min="0.1" 
            max="1" 
            step="0.1" 
            value="0.9"
            class="slider"
          >
          <small>Higher quality = larger file sizes</small>
        </div>
      </form>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <button id="openApp" class="btn btn-outline">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
          <polyline points="15,3 21,3 21,9"></polyline>
          <line x1="10" y1="14" x2="21" y2="3"></line>
        </svg>
        Open Web App
      </button>
    </div>

    <!-- Message Display -->
    <div id="message" class="message" style="display: none;"></div>

    <!-- Footer -->
    <div class="footer">
      <small>Scribe Pro v2.0 - Professional Tutorial Recording</small>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
