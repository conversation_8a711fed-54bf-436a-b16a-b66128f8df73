(()=>{"use strict";let e=!1,t={},n=0,o=null,i=0;function r(){console.log("Scribe Pro content script loaded"),chrome.runtime.sendMessage({type:"GET_RECORDING_STATUS",timestamp:Date.now()}).then(c).catch(()=>{}),chrome.runtime.onMessage.addListener(a),function(){let e;document.addEventListener("click",s,!0),document.addEventListener("input",d,!0),document.addEventListener("change",l,!0),document.addEventListener("submit",u,!0),window.addEventListener("beforeunload",h),window.addEventListener("popstate",h),document.addEventListener("scroll",()=>{clearTimeout(e),e=setTimeout(m,500)},!0),t.captureHovers&&document.addEventListener("mouseenter",p,!0),document.addEventListener("focus",g,!0),document.addEventListener("change",f,!0),document.addEventListener("keydown",b,!0)}()}function a(n,r,a){switch(n.type){case"RECORDING_STARTED":c=n.payload,e=!0,t=c.options||{},i=0,C(),console.log("Content script: Recording started");break;case"RECORDING_STOPPED":e=!1,o&&(o.remove(),o=null),console.log("Content script: Recording stopped");break;case"RECORDING_PAUSED":e=!1,S("PAUSED");break;case"RECORDING_RESUMED":e=!0,S("RECORDING")}var c}function c(n){n.isRecording&&(e=!0,t=n.options||{},i=n.stepCount||0,C())}function s(o){if(!e||!o.target)return;const i=o.target;if(i.closest(".scribe-recording-indicator"))return;const r=Date.now();if(r-n<300)return;n=r;const a=w(i);o.clientX,o.clientY,t.highlightElements&&function(e){const t=e.style.cssText;e.style.cssText+="\n    outline: 3px solid #3b82f6 !important;\n    outline-offset: 2px !important;\n    background-color: rgba(59, 130, 246, 0.1) !important;\n    transition: all 0.2s ease !important;\n  ",setTimeout(()=>{e.style.cssText=t},1e3)}(i),x({action:"click",element:a.selector,elementText:a.text,metadata:N()})}function d(o){if(!e||!t.captureFormInputs)return;const i=o.target;if(!i||"password"===i.type)return;const r=Date.now();if(r-n<300)return;n=r;const a=w(i);x({action:"type",element:a.selector,elementText:a.text||i.placeholder||"",inputValue:t.blurSensitiveData&&T(i)?"[HIDDEN]":i.value,metadata:N()})}function l(t){if(!e)return;const n=t.target;if(!n)return;const o=w(n);let i="",r="change";if("SELECT"===n.tagName){const e=n,t=e.options[e.selectedIndex];i=t?t.text:e.value,r="select"}else"checkbox"!==n.type&&"radio"!==n.type||(i=n.checked?"checked":"unchecked",r="checkbox"===n.type?"check":"select");x({action:r,element:o.selector,elementText:o.text,inputValue:i,metadata:N()})}function u(t){if(!e)return;const n=t.target;n&&x({action:"submit",element:w(n).selector,elementText:"Submit form",metadata:N()})}function m(){e&&t.captureScrolling&&x({action:"scroll",element:"window",elementText:"Scroll page",metadata:N()})}function p(n){if(!e||!t.captureHovers)return;const o=n.target;if(!o)return;if(!(["A","BUTTON","INPUT","TEXTAREA","SELECT"].includes((i=o).tagName)||["button","link","menuitem","tab"].includes(i.getAttribute("role")||"")||i.hasAttribute("onclick")||i.hasAttribute("tabindex")))return;var i;const r=w(o);x({action:"hover",element:r.selector,elementText:r.text,metadata:N()})}function g(t){if(!e)return;const n=t.target;if(!n||!y(n))return;const o=w(n);x({action:"focus",element:o.selector,elementText:o.text||n.placeholder||"",metadata:N()})}function f(e){}function b(t){if(e){if(t.ctrlKey||t.metaKey){const e=t.key.toLowerCase();["s","c","v","z","y"].includes(e)&&x({action:"keyboard",element:"window",elementText:`Keyboard shortcut: ${t.ctrlKey?"Ctrl":"Cmd"}+${e.toUpperCase()}`,metadata:N()})}"Enter"===t.key&&t.target&&y(t.target)&&x({action:"keyboard",element:w(t.target).selector,elementText:"Press Enter",metadata:N()})}}function h(){e&&t.captureNavigation&&x({action:"navigate",element:"window",elementText:"Navigate away from page",url:window.location.href,metadata:N()})}function x(t){e&&(i++,function(){const e=document.getElementById("scribe-step-count");e&&(e.textContent=i.toString())}(),chrome.runtime.sendMessage({type:"RECORD_STEP",payload:{step:t},timestamp:Date.now()}).catch(e=>{console.error("Failed to send interaction to background:",e)}),console.log("Interaction recorded:",t.action,t.elementText))}function w(e){const t=e.getBoundingClientRect();return{selector:E(e),text:v(e),tagName:e.tagName.toLowerCase(),type:e.type||void 0,placeholder:e.placeholder||void 0,ariaLabel:e.getAttribute("aria-label")||void 0,title:e.getAttribute("title")||void 0,id:e.id||void 0,className:e.className||void 0,bounds:{x:t.x,y:t.y,width:t.width,height:t.height,top:t.top,right:t.right,bottom:t.bottom,left:t.left,toJSON:t.toJSON}}}function E(e){if(e.id)return`#${e.id}`;const t=e.getAttribute("data-testid")||e.getAttribute("data-test");if(t)return`[data-testid="${t}"]`;const n=e.getAttribute("aria-label");if(n)return`[aria-label="${n}"]`;const o=e.getAttribute("name");if(o)return`[name="${o}"]`;const i=[];let r=e;for(;r&&r!==document.body;){let e=r.tagName.toLowerCase();if(r.className){const t=r.className.split(" ").filter(e=>e.length>0);if(t.length>0){const n=t.find(e=>!e.startsWith("css-")&&!e.match(/^[a-z0-9]{6,}$/)&&e.length>2);n&&(e+=`.${n}`)}}const t=Array.from(r.parentElement?.children||[]).filter(e=>e.tagName===r.tagName);t.length>1&&(e+=`:nth-child(${t.indexOf(r)+1})`),i.unshift(e),r=r.parentElement}return i.join(" > ")}function v(e){if(y(e)){const t=function(e){if(e.id){const t=document.querySelector(`label[for="${e.id}"]`);if(t)return t}const t=e.closest("label");if(t)return t;let n=e.previousElementSibling;for(;n;){if("LABEL"===n.tagName)return n;n=n.previousElementSibling}return null}(e);if(t)return t.textContent?.trim()||"";const n=e.placeholder;if(n)return n;const o=e.getAttribute("aria-label");if(o)return o}if("BUTTON"===e.tagName||"A"===e.tagName)return e.textContent?.trim()||"";if("IMG"===e.tagName)return e.alt||"";if("SELECT"===e.tagName){const t=e,n=t.options[t.selectedIndex];return n?n.text:""}const t=e.textContent?.trim()||"";return t.length>50?t.substring(0,50)+"...":t}function y(e){return["INPUT","TEXTAREA","SELECT","BUTTON"].includes(e.tagName)}function T(e){if(["password","email"].includes(e.type))return!0;const t=(e.name||e.id||e.className).toLowerCase();return["password","ssn","credit","card","cvv"].some(e=>t.includes(e))}function N(){return{viewport:{width:window.innerWidth,height:window.innerHeight},scrollPosition:{x:window.scrollX,y:window.scrollY},pageTitle:document.title,userAgent:navigator.userAgent,timestamp:Date.now()}}function C(){if(o)return;o=document.createElement("div"),o.className="scribe-recording-indicator",o.innerHTML='\n    <div class="scribe-indicator-content">\n      <div class="scribe-recording-dot"></div>\n      <span class="scribe-recording-text">Recording</span>\n      <span class="scribe-step-counter">Step: <span id="scribe-step-count">0</span></span>\n    </div>\n  ';const e=document.createElement("style");e.textContent="\n    .scribe-recording-indicator {\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      z-index: 999999;\n      background: rgba(0, 0, 0, 0.9);\n      color: white;\n      padding: 12px 16px;\n      border-radius: 8px;\n      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n      font-size: 14px;\n      font-weight: 500;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n      backdrop-filter: blur(10px);\n      border: 1px solid rgba(255, 255, 255, 0.1);\n      user-select: none;\n      pointer-events: none;\n    }\n\n    .scribe-indicator-content {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .scribe-recording-dot {\n      width: 8px;\n      height: 8px;\n      background: #ef4444;\n      border-radius: 50%;\n      animation: scribe-pulse 1.5s infinite;\n    }\n\n    .scribe-recording-text {\n      color: #ef4444;\n      font-weight: 600;\n    }\n\n    .scribe-step-counter {\n      color: #94a3b8;\n      font-size: 12px;\n    }\n\n    @keyframes scribe-pulse {\n      0%, 100% { opacity: 1; transform: scale(1); }\n      50% { opacity: 0.5; transform: scale(1.2); }\n    }\n\n    .scribe-recording-indicator.paused .scribe-recording-dot {\n      background: #f59e0b;\n      animation: none;\n    }\n\n    .scribe-recording-indicator.paused .scribe-recording-text {\n      color: #f59e0b;\n    }\n  ",document.head.appendChild(e),document.body.appendChild(o)}function S(e){if(!o)return;const t=o.querySelector(".scribe-recording-text");t&&(t.textContent="RECORDING"===e?"Recording":"Paused"),"PAUSED"===e?o.classList.add("paused"):o.classList.remove("paused")}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",r):r();let L=window.location.href;new MutationObserver(()=>{window.location.href!==L&&(L=window.location.href,e&&t.captureNavigation&&x({action:"navigate",element:"window",elementText:`Navigate to ${document.title}`,url:L,metadata:N()}))}).observe(document.body,{childList:!0,subtree:!0}),console.log("Scribe Pro content script initialized")})();