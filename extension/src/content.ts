/**
 * Scribe Pro Chrome Extension - Content Script
 * Intelligent interaction detection and recording for all user actions
 */

import type {
  InteractionEvent,
  ElementInfo,
  RecordedStep,
  Message,
  RecordingState,
  StepMetadata
} from './types';

// Recording state
let isRecording = false;
let recordingOptions: any = {};
let lastInteractionTime = 0;
let interactionQueue: InteractionEvent[] = [];
let recordingIndicator: HTMLElement | null = null;
let stepCounter = 0;

// Debounce settings
const INTERACTION_DEBOUNCE = 300; // ms
const SCROLL_DEBOUNCE = 500; // ms

/**
 * Initialize content script
 */
function initialize(): void {
  console.log('Scribe Pro content script loaded');
  
  // Check if recording is active
  chrome.runtime.sendMessage({ type: 'GET_RECORDING_STATUS', timestamp: Date.now() })
    .then(handleRecordingStatus)
    .catch(() => {
      // Extension might not be ready yet
    });

  // Listen for messages from background script
  chrome.runtime.onMessage.addListener(handleMessage);

  // Set up interaction listeners
  setupInteractionListeners();
}

/**
 * Handle messages from background script
 */
function handleMessage(message: Message, sender: any, sendResponse: Function): void {
  switch (message.type) {
    case 'RECORDING_STARTED':
      startRecording(message.payload);
      break;
      
    case 'RECORDING_STOPPED':
      stopRecording();
      break;
      
    case 'RECORDING_PAUSED':
      pauseRecording();
      break;
      
    case 'RECORDING_RESUMED':
      resumeRecording();
      break;
      
    default:
      // Ignore unknown messages
      break;
  }
}

/**
 * Handle recording status response
 */
function handleRecordingStatus(status: RecordingState & { options?: any }): void {
  if (status.isRecording) {
    isRecording = true;
    recordingOptions = status.options || {};
    stepCounter = status.stepCount || 0;
    showRecordingIndicator();
  }
}

/**
 * Start recording interactions
 */
function startRecording(payload: any): void {
  isRecording = true;
  recordingOptions = payload.options || {};
  stepCounter = 0;
  showRecordingIndicator();
  console.log('Content script: Recording started');
}

/**
 * Stop recording interactions
 */
function stopRecording(): void {
  isRecording = false;
  hideRecordingIndicator();
  console.log('Content script: Recording stopped');
}

/**
 * Pause recording
 */
function pauseRecording(): void {
  isRecording = false;
  updateRecordingIndicator('PAUSED');
}

/**
 * Resume recording
 */
function resumeRecording(): void {
  isRecording = true;
  updateRecordingIndicator('RECORDING');
}

/**
 * Set up all interaction event listeners
 */
function setupInteractionListeners(): void {
  // Click events
  document.addEventListener('click', handleClickEvent, true);
  
  // Input events
  document.addEventListener('input', handleInputEvent, true);
  document.addEventListener('change', handleChangeEvent, true);
  
  // Form events
  document.addEventListener('submit', handleSubmitEvent, true);
  
  // Navigation events
  window.addEventListener('beforeunload', handleNavigationEvent);
  window.addEventListener('popstate', handleNavigationEvent);
  
  // Scroll events (debounced)
  let scrollTimeout: NodeJS.Timeout;
  document.addEventListener('scroll', () => {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(handleScrollEvent, SCROLL_DEBOUNCE);
  }, true);
  
  // Hover events (if enabled)
  if (recordingOptions.captureHovers) {
    document.addEventListener('mouseenter', handleHoverEvent, true);
  }
  
  // Focus events for form fields
  document.addEventListener('focus', handleFocusEvent, true);
  
  // Selection events
  document.addEventListener('change', handleSelectEvent, true);
  
  // Keyboard events for shortcuts
  document.addEventListener('keydown', handleKeyboardEvent, true);
}

/**
 * Handle click events
 */
function handleClickEvent(event: MouseEvent): void {
  if (!isRecording || !event.target) return;
  
  const target = event.target as Element;
  
  // Skip if clicking on recording indicator
  if (target.closest('.scribe-recording-indicator')) return;
  
  // Debounce rapid clicks
  const now = Date.now();
  if (now - lastInteractionTime < INTERACTION_DEBOUNCE) return;
  lastInteractionTime = now;
  
  const elementInfo = getElementInfo(target);
  const coordinates = { x: event.clientX, y: event.clientY };
  
  // Highlight element briefly
  if (recordingOptions.highlightElements) {
    highlightElement(target);
  }
  
  // Record the click
  recordInteraction({
    action: 'click',
    element: elementInfo.selector,
    elementText: elementInfo.text,
    metadata: getCurrentMetadata()
  });
}

/**
 * Handle input events (typing)
 */
function handleInputEvent(event: Event): void {
  if (!isRecording || !recordingOptions.captureFormInputs) return;
  
  const target = event.target as HTMLInputElement;
  if (!target || target.type === 'password') return;
  
  // Debounce rapid typing
  const now = Date.now();
  if (now - lastInteractionTime < INTERACTION_DEBOUNCE) return;
  lastInteractionTime = now;
  
  const elementInfo = getElementInfo(target);
  
  recordInteraction({
    action: 'type',
    element: elementInfo.selector,
    elementText: elementInfo.text || target.placeholder || '',
    inputValue: recordingOptions.blurSensitiveData && isSensitiveField(target) ? '[HIDDEN]' : target.value,
    metadata: getCurrentMetadata()
  });
}

/**
 * Handle change events (dropdowns, checkboxes, radio buttons)
 */
function handleChangeEvent(event: Event): void {
  if (!isRecording) return;
  
  const target = event.target as HTMLSelectElement | HTMLInputElement;
  if (!target) return;
  
  const elementInfo = getElementInfo(target);
  let value = '';
  let action = 'change';
  
  if (target.tagName === 'SELECT') {
    const selectElement = target as HTMLSelectElement;
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    value = selectedOption ? selectedOption.text : selectElement.value;
    action = 'select';
  } else if (target.type === 'checkbox' || target.type === 'radio') {
    value = (target as HTMLInputElement).checked ? 'checked' : 'unchecked';
    action = target.type === 'checkbox' ? 'check' : 'select';
  }
  
  recordInteraction({
    action: action as any,
    element: elementInfo.selector,
    elementText: elementInfo.text,
    inputValue: value,
    metadata: getCurrentMetadata()
  });
}

/**
 * Handle form submit events
 */
function handleSubmitEvent(event: Event): void {
  if (!isRecording) return;
  
  const target = event.target as HTMLFormElement;
  if (!target) return;
  
  const elementInfo = getElementInfo(target);
  
  recordInteraction({
    action: 'submit',
    element: elementInfo.selector,
    elementText: 'Submit form',
    metadata: getCurrentMetadata()
  });
}

/**
 * Handle scroll events
 */
function handleScrollEvent(): void {
  if (!isRecording || !recordingOptions.captureScrolling) return;
  
  recordInteraction({
    action: 'scroll',
    element: 'window',
    elementText: 'Scroll page',
    metadata: getCurrentMetadata()
  });
}

/**
 * Handle hover events
 */
function handleHoverEvent(event: MouseEvent): void {
  if (!isRecording || !recordingOptions.captureHovers) return;
  
  const target = event.target as Element;
  if (!target) return;
  
  // Only record hover on interactive elements
  if (!isInteractiveElement(target)) return;
  
  const elementInfo = getElementInfo(target);
  
  recordInteraction({
    action: 'hover',
    element: elementInfo.selector,
    elementText: elementInfo.text,
    metadata: getCurrentMetadata()
  });
}

/**
 * Handle focus events
 */
function handleFocusEvent(event: FocusEvent): void {
  if (!isRecording) return;
  
  const target = event.target as Element;
  if (!target || !isFormElement(target)) return;
  
  const elementInfo = getElementInfo(target);
  
  recordInteraction({
    action: 'focus',
    element: elementInfo.selector,
    elementText: elementInfo.text || (target as HTMLInputElement).placeholder || '',
    metadata: getCurrentMetadata()
  });
}

/**
 * Handle select events
 */
function handleSelectEvent(event: Event): void {
  // This is handled by handleChangeEvent
}

/**
 * Handle keyboard events
 */
function handleKeyboardEvent(event: KeyboardEvent): void {
  if (!isRecording) return;
  
  // Record important keyboard shortcuts
  if (event.ctrlKey || event.metaKey) {
    const key = event.key.toLowerCase();
    if (['s', 'c', 'v', 'z', 'y'].includes(key)) {
      recordInteraction({
        action: 'keyboard',
        element: 'window',
        elementText: `Keyboard shortcut: ${event.ctrlKey ? 'Ctrl' : 'Cmd'}+${key.toUpperCase()}`,
        metadata: getCurrentMetadata()
      });
    }
  }
  
  // Record Enter key on form elements
  if (event.key === 'Enter' && event.target && isFormElement(event.target as Element)) {
    const elementInfo = getElementInfo(event.target as Element);
    recordInteraction({
      action: 'keyboard',
      element: elementInfo.selector,
      elementText: 'Press Enter',
      metadata: getCurrentMetadata()
    });
  }
}

/**
 * Handle navigation events
 */
function handleNavigationEvent(): void {
  if (!isRecording || !recordingOptions.captureNavigation) return;
  
  recordInteraction({
    action: 'navigate',
    element: 'window',
    elementText: 'Navigate away from page',
    url: window.location.href,
    metadata: getCurrentMetadata()
  });
}

/**
 * Record an interaction by sending it to background script
 */
function recordInteraction(interaction: Partial<RecordedStep>): void {
  if (!isRecording) return;

  stepCounter++;
  updateStepCounter();

  // Send to background script
  chrome.runtime.sendMessage({
    type: 'RECORD_STEP',
    payload: { step: interaction },
    timestamp: Date.now()
  }).catch((error) => {
    console.error('Failed to send interaction to background:', error);
  });

  console.log('Interaction recorded:', interaction.action, interaction.elementText);
}

// Utility Functions

/**
 * Get comprehensive element information
 */
function getElementInfo(element: Element): ElementInfo {
  const bounds = element.getBoundingClientRect();

  return {
    selector: generateElementSelector(element),
    text: getElementText(element),
    tagName: element.tagName.toLowerCase(),
    type: (element as HTMLInputElement).type || undefined,
    placeholder: (element as HTMLInputElement).placeholder || undefined,
    ariaLabel: element.getAttribute('aria-label') || undefined,
    title: element.getAttribute('title') || undefined,
    id: element.id || undefined,
    className: element.className || undefined,
    bounds: {
      x: bounds.x,
      y: bounds.y,
      width: bounds.width,
      height: bounds.height,
      top: bounds.top,
      right: bounds.right,
      bottom: bounds.bottom,
      left: bounds.left,
      toJSON: bounds.toJSON
    }
  };
}

/**
 * Generate a robust CSS selector for an element
 */
function generateElementSelector(element: Element): string {
  // Try ID first (most specific)
  if (element.id) {
    return `#${element.id}`;
  }

  // Try data attributes
  const dataTestId = element.getAttribute('data-testid') || element.getAttribute('data-test');
  if (dataTestId) {
    return `[data-testid="${dataTestId}"]`;
  }

  // Try aria-label
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel) {
    return `[aria-label="${ariaLabel}"]`;
  }

  // Try name attribute for form elements
  const name = element.getAttribute('name');
  if (name) {
    return `[name="${name}"]`;
  }

  // Build path from root
  const path: string[] = [];
  let current: Element | null = element;

  while (current && current !== document.body) {
    let selector = current.tagName.toLowerCase();

    // Add class if unique enough
    if (current.className) {
      const classes = current.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        // Use first class that seems meaningful
        const meaningfulClass = classes.find(c =>
          !c.startsWith('css-') &&
          !c.match(/^[a-z0-9]{6,}$/) && // Avoid generated classes
          c.length > 2
        );
        if (meaningfulClass) {
          selector += `.${meaningfulClass}`;
        }
      }
    }

    // Add nth-child if needed for uniqueness
    const siblings = Array.from(current.parentElement?.children || [])
      .filter(sibling => sibling.tagName === current!.tagName);

    if (siblings.length > 1) {
      const index = siblings.indexOf(current) + 1;
      selector += `:nth-child(${index})`;
    }

    path.unshift(selector);
    current = current.parentElement;
  }

  return path.join(' > ');
}

/**
 * Get meaningful text from element
 */
function getElementText(element: Element): string {
  // For form elements, try label first
  if (isFormElement(element)) {
    const label = findAssociatedLabel(element);
    if (label) return label.textContent?.trim() || '';

    // Try placeholder
    const placeholder = (element as HTMLInputElement).placeholder;
    if (placeholder) return placeholder;

    // Try aria-label
    const ariaLabel = element.getAttribute('aria-label');
    if (ariaLabel) return ariaLabel;
  }

  // For buttons and links, get direct text
  if (element.tagName === 'BUTTON' || element.tagName === 'A') {
    return element.textContent?.trim() || '';
  }

  // For images, try alt text
  if (element.tagName === 'IMG') {
    return (element as HTMLImageElement).alt || '';
  }

  // For select elements, try the selected option
  if (element.tagName === 'SELECT') {
    const select = element as HTMLSelectElement;
    const selectedOption = select.options[select.selectedIndex];
    return selectedOption ? selectedOption.text : '';
  }

  // Get first meaningful text content
  const text = element.textContent?.trim() || '';
  return text.length > 50 ? text.substring(0, 50) + '...' : text;
}

/**
 * Find associated label for form element
 */
function findAssociatedLabel(element: Element): HTMLLabelElement | null {
  // Try label with for attribute
  if (element.id) {
    const label = document.querySelector(`label[for="${element.id}"]`) as HTMLLabelElement;
    if (label) return label;
  }

  // Try parent label
  const parentLabel = element.closest('label') as HTMLLabelElement;
  if (parentLabel) return parentLabel;

  // Try previous sibling label
  let sibling = element.previousElementSibling;
  while (sibling) {
    if (sibling.tagName === 'LABEL') {
      return sibling as HTMLLabelElement;
    }
    sibling = sibling.previousElementSibling;
  }

  return null;
}

/**
 * Check if element is a form element
 */
function isFormElement(element: Element): boolean {
  const formTags = ['INPUT', 'TEXTAREA', 'SELECT', 'BUTTON'];
  return formTags.includes(element.tagName);
}

/**
 * Check if element is interactive
 */
function isInteractiveElement(element: Element): boolean {
  const interactiveTags = ['A', 'BUTTON', 'INPUT', 'TEXTAREA', 'SELECT'];
  const interactiveRoles = ['button', 'link', 'menuitem', 'tab'];

  return interactiveTags.includes(element.tagName) ||
         interactiveRoles.includes(element.getAttribute('role') || '') ||
         element.hasAttribute('onclick') ||
         element.hasAttribute('tabindex');
}

/**
 * Check if field contains sensitive data
 */
function isSensitiveField(element: HTMLInputElement): boolean {
  const sensitiveTypes = ['password', 'email'];
  const sensitiveNames = ['password', 'ssn', 'credit', 'card', 'cvv'];

  if (sensitiveTypes.includes(element.type)) return true;

  const name = (element.name || element.id || element.className).toLowerCase();
  return sensitiveNames.some(sensitive => name.includes(sensitive));
}

/**
 * Get current page metadata
 */
function getCurrentMetadata(): StepMetadata {
  return {
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    scrollPosition: {
      x: window.scrollX,
      y: window.scrollY
    },
    pageTitle: document.title,
    userAgent: navigator.userAgent,
    timestamp: Date.now()
  };
}

/**
 * Highlight element briefly
 */
function highlightElement(element: Element): void {
  const originalStyle = (element as HTMLElement).style.cssText;
  const highlightStyle = `
    outline: 3px solid #3b82f6 !important;
    outline-offset: 2px !important;
    background-color: rgba(59, 130, 246, 0.1) !important;
    transition: all 0.2s ease !important;
  `;

  (element as HTMLElement).style.cssText += highlightStyle;

  setTimeout(() => {
    (element as HTMLElement).style.cssText = originalStyle;
  }, 1000);
}

// Recording Indicator UI

/**
 * Show recording indicator
 */
function showRecordingIndicator(): void {
  if (recordingIndicator) return;

  recordingIndicator = document.createElement('div');
  recordingIndicator.className = 'scribe-recording-indicator';
  recordingIndicator.innerHTML = `
    <div class="scribe-indicator-content">
      <div class="scribe-recording-dot"></div>
      <span class="scribe-recording-text">Recording</span>
      <span class="scribe-step-counter">Step: <span id="scribe-step-count">0</span></span>
    </div>
  `;

  // Add styles
  const style = document.createElement('style');
  style.textContent = `
    .scribe-recording-indicator {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 999999;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      user-select: none;
      pointer-events: none;
    }

    .scribe-indicator-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .scribe-recording-dot {
      width: 8px;
      height: 8px;
      background: #ef4444;
      border-radius: 50%;
      animation: scribe-pulse 1.5s infinite;
    }

    .scribe-recording-text {
      color: #ef4444;
      font-weight: 600;
    }

    .scribe-step-counter {
      color: #94a3b8;
      font-size: 12px;
    }

    @keyframes scribe-pulse {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.5; transform: scale(1.2); }
    }

    .scribe-recording-indicator.paused .scribe-recording-dot {
      background: #f59e0b;
      animation: none;
    }

    .scribe-recording-indicator.paused .scribe-recording-text {
      color: #f59e0b;
    }
  `;

  document.head.appendChild(style);
  document.body.appendChild(recordingIndicator);
}

/**
 * Hide recording indicator
 */
function hideRecordingIndicator(): void {
  if (recordingIndicator) {
    recordingIndicator.remove();
    recordingIndicator = null;
  }
}

/**
 * Update recording indicator status
 */
function updateRecordingIndicator(status: 'RECORDING' | 'PAUSED'): void {
  if (!recordingIndicator) return;

  const textElement = recordingIndicator.querySelector('.scribe-recording-text');
  if (textElement) {
    textElement.textContent = status === 'RECORDING' ? 'Recording' : 'Paused';
  }

  if (status === 'PAUSED') {
    recordingIndicator.classList.add('paused');
  } else {
    recordingIndicator.classList.remove('paused');
  }
}

/**
 * Update step counter
 */
function updateStepCounter(): void {
  const counterElement = document.getElementById('scribe-step-count');
  if (counterElement) {
    counterElement.textContent = stepCounter.toString();
  }
}

// Element annotation functions

/**
 * Add annotation overlay to element
 */
function addElementAnnotation(element: Element, annotation: { type: string; text?: string }): void {
  const bounds = element.getBoundingClientRect();
  const overlay = document.createElement('div');
  overlay.className = 'scribe-element-annotation';

  overlay.style.cssText = `
    position: fixed;
    top: ${bounds.top - 30}px;
    left: ${bounds.left}px;
    z-index: 999998;
    background: #3b82f6;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  `;

  overlay.textContent = annotation.text || annotation.type;
  document.body.appendChild(overlay);

  // Remove after 2 seconds
  setTimeout(() => {
    overlay.remove();
  }, 2000);
}

/**
 * Create screenshot annotation
 */
function createScreenshotAnnotation(element: Element): any {
  const bounds = element.getBoundingClientRect();

  return {
    id: `annotation-${Date.now()}`,
    type: 'highlight',
    x: bounds.left,
    y: bounds.top,
    width: bounds.width,
    height: bounds.height,
    color: '#3b82f6',
    style: {
      borderRadius: '4px',
      opacity: 0.3
    }
  };
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// Handle page navigation in SPAs
let currentUrl = window.location.href;
const observer = new MutationObserver(() => {
  if (window.location.href !== currentUrl) {
    currentUrl = window.location.href;
    if (isRecording && recordingOptions.captureNavigation) {
      recordInteraction({
        action: 'navigate',
        element: 'window',
        elementText: `Navigate to ${document.title}`,
        url: currentUrl,
        metadata: getCurrentMetadata()
      });
    }
  }
});

observer.observe(document.body, {
  childList: true,
  subtree: true
});

console.log('Scribe Pro content script initialized');
