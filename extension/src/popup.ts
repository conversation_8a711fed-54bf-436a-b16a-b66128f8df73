/**
 * Scribe Pro Chrome Extension - Popup Interface
 * User interface for controlling recording and managing tutorials
 */

import type { RecordingState, RecordingOptions, Tutorial } from './types';

// DOM elements
let startButton: HTMLButtonElement;
let stopButton: HTMLButtonElement;
let pauseButton: HTMLButtonElement;
let statusElement: HTMLElement;
let tutorialTitleInput: HTMLInputElement;
let stepCountElement: HTMLElement;
let optionsForm: HTMLFormElement;
let tutorialsList: HTMLElement;

// State
let currentState: RecordingState & { tutorial?: Tutorial; options?: RecordingOptions } = {
  isRecording: false,
  currentTutorialId: null,
  stepCount: 0,
  startTime: null
};

/**
 * Initialize popup interface
 */
document.addEventListener('DOMContentLoaded', async () => {
  initializeElements();
  setupEventListeners();
  await loadCurrentState();
  updateUI();
});

/**
 * Initialize DOM elements
 */
function initializeElements(): void {
  startButton = document.getElementById('startButton') as HTMLButtonElement;
  stopButton = document.getElementById('stopButton') as HTMLButtonElement;
  pauseButton = document.getElementById('pauseButton') as HTMLButtonElement;
  statusElement = document.getElementById('status') as HTMLElement;
  tutorialTitleInput = document.getElementById('tutorialTitle') as HTMLInputElement;
  stepCountElement = document.getElementById('stepCount') as HTMLElement;
  optionsForm = document.getElementById('optionsForm') as HTMLFormElement;
  tutorialsList = document.getElementById('tutorialsList') as HTMLElement;
}

/**
 * Set up event listeners
 */
function setupEventListeners(): void {
  startButton.addEventListener('click', handleStartRecording);
  stopButton.addEventListener('click', handleStopRecording);
  pauseButton.addEventListener('click', handlePauseRecording);
  
  // Options form
  optionsForm.addEventListener('change', handleOptionsChange);
  
  // Tutorial title input
  tutorialTitleInput.addEventListener('input', () => {
    startButton.disabled = !tutorialTitleInput.value.trim();
  });
  
  // Open web app button
  const openAppButton = document.getElementById('openApp');
  if (openAppButton) {
    openAppButton.addEventListener('click', () => {
      chrome.tabs.create({ url: 'http://localhost:5173' });
      window.close();
    });
  }
  
  // Settings button
  const settingsButton = document.getElementById('settings');
  if (settingsButton) {
    settingsButton.addEventListener('click', toggleSettings);
  }
}

/**
 * Load current recording state
 */
async function loadCurrentState(): Promise<void> {
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'GET_RECORDING_STATUS',
      timestamp: Date.now()
    });
    
    if (response) {
      currentState = response;
    }
  } catch (error) {
    console.error('Failed to load current state:', error);
  }
}

/**
 * Update UI based on current state
 */
function updateUI(): void {
  if (currentState.isRecording) {
    // Recording state
    startButton.style.display = 'none';
    stopButton.style.display = 'inline-block';
    pauseButton.style.display = 'inline-block';
    pauseButton.textContent = 'Pause';
    
    statusElement.textContent = 'Recording in progress...';
    statusElement.className = 'status recording';
    
    stepCountElement.textContent = `Steps recorded: ${currentState.stepCount}`;
    stepCountElement.style.display = 'block';
    
    tutorialTitleInput.disabled = true;
    
    // Show current tutorial info
    if (currentState.tutorial) {
      const tutorialInfo = document.getElementById('currentTutorial');
      if (tutorialInfo) {
        tutorialInfo.innerHTML = `
          <h3>Current Tutorial</h3>
          <p><strong>${currentState.tutorial.title}</strong></p>
          <p>Started: ${new Date(currentState.tutorial.createdAt).toLocaleTimeString()}</p>
        `;
        tutorialInfo.style.display = 'block';
      }
    }
  } else {
    // Not recording state
    startButton.style.display = 'inline-block';
    stopButton.style.display = 'none';
    pauseButton.style.display = 'none';
    
    statusElement.textContent = 'Ready to record';
    statusElement.className = 'status ready';
    
    stepCountElement.style.display = 'none';
    tutorialTitleInput.disabled = false;
    
    // Hide current tutorial info
    const tutorialInfo = document.getElementById('currentTutorial');
    if (tutorialInfo) {
      tutorialInfo.style.display = 'none';
    }
  }
  
  // Update options form
  if (currentState.options) {
    updateOptionsForm(currentState.options);
  }
}

/**
 * Handle start recording
 */
async function handleStartRecording(): Promise<void> {
  const title = tutorialTitleInput.value.trim();
  if (!title) {
    showError('Please enter a tutorial title');
    return;
  }
  
  try {
    startButton.disabled = true;
    startButton.textContent = 'Starting...';
    
    const options = getOptionsFromForm();
    
    const response = await chrome.runtime.sendMessage({
      type: 'START_RECORDING',
      payload: {
        tutorialTitle: title,
        options
      },
      timestamp: Date.now()
    });
    
    if (response.success) {
      currentState.isRecording = true;
      currentState.tutorial = response.tutorial;
      currentState.stepCount = 0;
      updateUI();
      showSuccess('Recording started!');
    } else {
      throw new Error(response.error || 'Failed to start recording');
    }
  } catch (error) {
    console.error('Failed to start recording:', error);
    showError(error instanceof Error ? error.message : 'Failed to start recording');
  } finally {
    startButton.disabled = false;
    startButton.textContent = 'Start Recording';
  }
}

/**
 * Handle stop recording
 */
async function handleStopRecording(): Promise<void> {
  try {
    stopButton.disabled = true;
    stopButton.textContent = 'Stopping...';
    
    const response = await chrome.runtime.sendMessage({
      type: 'STOP_RECORDING',
      timestamp: Date.now()
    });
    
    if (response.success) {
      currentState.isRecording = false;
      currentState.tutorial = undefined;
      currentState.stepCount = 0;
      updateUI();
      showSuccess(`Recording stopped! ${response.tutorial?.steps?.length || 0} steps recorded.`);
      
      // Clear tutorial title for next recording
      tutorialTitleInput.value = '';
    } else {
      throw new Error(response.error || 'Failed to stop recording');
    }
  } catch (error) {
    console.error('Failed to stop recording:', error);
    showError(error instanceof Error ? error.message : 'Failed to stop recording');
  } finally {
    stopButton.disabled = false;
    stopButton.textContent = 'Stop Recording';
  }
}

/**
 * Handle pause/resume recording
 */
async function handlePauseRecording(): Promise<void> {
  try {
    const isPaused = pauseButton.textContent === 'Resume';
    const messageType = isPaused ? 'RESUME_RECORDING' : 'PAUSE_RECORDING';
    
    pauseButton.disabled = true;
    
    const response = await chrome.runtime.sendMessage({
      type: messageType,
      timestamp: Date.now()
    });
    
    if (response.success) {
      pauseButton.textContent = isPaused ? 'Pause' : 'Resume';
      statusElement.textContent = isPaused ? 'Recording in progress...' : 'Recording paused';
      statusElement.className = isPaused ? 'status recording' : 'status paused';
    } else {
      throw new Error(response.error || 'Failed to pause/resume recording');
    }
  } catch (error) {
    console.error('Failed to pause/resume recording:', error);
    showError(error instanceof Error ? error.message : 'Failed to pause/resume recording');
  } finally {
    pauseButton.disabled = false;
  }
}

/**
 * Handle options form changes
 */
function handleOptionsChange(): void {
  const options = getOptionsFromForm();
  
  // Save options to storage
  chrome.storage.local.set({ recordingOptions: options });
}

/**
 * Get recording options from form
 */
function getOptionsFromForm(): RecordingOptions {
  const formData = new FormData(optionsForm);
  
  return {
    captureScreenshots: formData.get('captureScreenshots') === 'on',
    captureScrolling: formData.get('captureScrolling') === 'on',
    captureHovers: formData.get('captureHovers') === 'on',
    captureFormInputs: formData.get('captureFormInputs') === 'on',
    captureNavigation: formData.get('captureNavigation') === 'on',
    autoGenerateDescriptions: formData.get('autoGenerateDescriptions') === 'on',
    highlightElements: formData.get('highlightElements') === 'on',
    blurSensitiveData: formData.get('blurSensitiveData') === 'on',
    screenshotQuality: parseFloat(formData.get('screenshotQuality') as string) || 0.9
  };
}

/**
 * Update options form with current values
 */
function updateOptionsForm(options: RecordingOptions): void {
  const form = optionsForm;
  
  (form.querySelector('[name="captureScreenshots"]') as HTMLInputElement).checked = options.captureScreenshots;
  (form.querySelector('[name="captureScrolling"]') as HTMLInputElement).checked = options.captureScrolling;
  (form.querySelector('[name="captureHovers"]') as HTMLInputElement).checked = options.captureHovers;
  (form.querySelector('[name="captureFormInputs"]') as HTMLInputElement).checked = options.captureFormInputs;
  (form.querySelector('[name="captureNavigation"]') as HTMLInputElement).checked = options.captureNavigation;
  (form.querySelector('[name="autoGenerateDescriptions"]') as HTMLInputElement).checked = options.autoGenerateDescriptions;
  (form.querySelector('[name="highlightElements"]') as HTMLInputElement).checked = options.highlightElements;
  (form.querySelector('[name="blurSensitiveData"]') as HTMLInputElement).checked = options.blurSensitiveData;
  (form.querySelector('[name="screenshotQuality"]') as HTMLInputElement).value = options.screenshotQuality.toString();
}

/**
 * Toggle settings panel
 */
function toggleSettings(): void {
  const settingsPanel = document.getElementById('settingsPanel');
  if (settingsPanel) {
    settingsPanel.style.display = settingsPanel.style.display === 'none' ? 'block' : 'none';
  }
}

/**
 * Show success message
 */
function showSuccess(message: string): void {
  showMessage(message, 'success');
}

/**
 * Show error message
 */
function showError(message: string): void {
  showMessage(message, 'error');
}

/**
 * Show message with type
 */
function showMessage(message: string, type: 'success' | 'error'): void {
  const messageElement = document.getElementById('message');
  if (messageElement) {
    messageElement.textContent = message;
    messageElement.className = `message ${type}`;
    messageElement.style.display = 'block';
    
    setTimeout(() => {
      messageElement.style.display = 'none';
    }, 3000);
  }
}

// Auto-refresh state every 2 seconds when popup is open
setInterval(async () => {
  await loadCurrentState();
  updateUI();
}, 2000);
