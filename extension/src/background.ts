/**
 * Scribe Pro Chrome Extension - Background Service Worker
 * Handles recording state management, API communication, and screenshot capture
 */

import { v4 as uuidv4 } from 'uuid';
import type { 
  RecordingState, 
  RecordedStep, 
  Tutorial, 
  Message, 
  RecordingOptions,
  ScreenshotOptions,
  ApiResponse,
  UploadResponse,
  RecordingAnalytics
} from '@/types';

// Recording state management
let recordingState: RecordingState = {
  isRecording: false,
  currentTutorialId: null,
  stepCount: 0,
  startTime: null
};

let recordedSteps: RecordedStep[] = [];
let currentTutorial: Tutorial | null = null;
let recordingOptions: RecordingOptions = {
  captureScreenshots: true,
  captureScrolling: true,
  captureHovers: false,
  captureFormInputs: true,
  captureNavigation: true,
  autoGenerateDescriptions: true,
  highlightElements: true,
  blurSensitiveData: true,
  screenshotQuality: 0.9
};

// API configuration
const API_BASE_URL = 'http://localhost:3001/api';
let authToken: string | null = null;

/**
 * Initialize extension and load saved state
 */
chrome.runtime.onInstalled.addListener(async () => {
  console.log('Scribe Pro extension installed');
  await loadStoredState();
  await initializeAnalytics();
});

/**
 * Load recording state from storage
 */
async function loadStoredState(): Promise<void> {
  try {
    const result = await chrome.storage.local.get(['recordingState', 'authToken', 'recordingOptions']);
    
    if (result.recordingState) {
      recordingState = result.recordingState;
    }
    
    if (result.authToken) {
      authToken = result.authToken;
    }
    
    if (result.recordingOptions) {
      recordingOptions = { ...recordingOptions, ...result.recordingOptions };
    }
  } catch (error) {
    console.error('Failed to load stored state:', error);
  }
}

/**
 * Save recording state to storage
 */
async function saveState(): Promise<void> {
  try {
    await chrome.storage.local.set({
      recordingState,
      authToken,
      recordingOptions,
      currentTutorial,
      recordedSteps: recordedSteps.slice(-50) // Keep last 50 steps
    });
  } catch (error) {
    console.error('Failed to save state:', error);
  }
}

// Message handling
chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
  handleMessage(message, sender)
    .then(response => sendResponse(response))
    .catch(error => {
      console.error('Message handling error:', error);
      sendResponse({ success: false, error: error.message });
    });
  
  return true; // Keep message channel open for async response
});

/**
 * Handle messages from content scripts and popup
 */
async function handleMessage(message: Message, sender: chrome.runtime.MessageSender): Promise<any> {
  switch (message.type) {
    case 'START_RECORDING':
      return await startRecording(message.payload?.tutorialTitle, message.payload?.options);
      
    case 'STOP_RECORDING':
      return await stopRecording();
      
    case 'PAUSE_RECORDING':
      return await pauseRecording();
      
    case 'RESUME_RECORDING':
      return await resumeRecording();
      
    case 'GET_RECORDING_STATUS':
      return getRecordingStatus();
      
    case 'RECORD_STEP':
      return await recordStep(message.payload?.step, sender.tab);
      
    case 'TAKE_SCREENSHOT':
      return await takeScreenshot(sender.tab?.id, message.payload?.options);
      
    case 'UPDATE_STEP':
      return await updateStep(message.payload?.stepId, message.payload?.updates);
      
    case 'DELETE_STEP':
      return await deleteStep(message.payload?.stepId);
      
    default:
      throw new Error(`Unknown message type: ${message.type}`);
  }
}

/**
 * Start recording a new tutorial
 */
async function startRecording(
  tutorialTitle: string = 'New Tutorial',
  options?: Partial<RecordingOptions>
): Promise<{ success: boolean; tutorial?: Tutorial; error?: string }> {
  try {
    if (recordingState.isRecording) {
      throw new Error('Recording is already in progress');
    }

    // Merge recording options
    if (options) {
      recordingOptions = { ...recordingOptions, ...options };
    }

    // Create new tutorial via API
    const tutorial = await createTutorial(tutorialTitle);
    
    // Initialize recording state
    recordingState = {
      isRecording: true,
      currentTutorialId: tutorial.id,
      stepCount: 0,
      startTime: Date.now()
    };
    
    currentTutorial = tutorial;
    recordedSteps = [];

    // Update extension badge
    await chrome.action.setBadgeText({ text: 'REC' });
    await chrome.action.setBadgeBackgroundColor({ color: '#dc2626' });

    // Notify all tabs that recording started
    await notifyAllTabs('RECORDING_STARTED', { tutorialId: tutorial.id });

    // Save state
    await saveState();

    console.log('Recording started:', tutorial.id);
    
    return { success: true, tutorial };
  } catch (error) {
    console.error('Failed to start recording:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Stop recording and save tutorial
 */
async function stopRecording(): Promise<{ success: boolean; tutorial?: Tutorial; error?: string }> {
  try {
    if (!recordingState.isRecording || !currentTutorial) {
      throw new Error('No recording in progress');
    }

    // Save recorded steps to backend
    if (recordedSteps.length > 0) {
      await saveTutorialSteps(currentTutorial.id, recordedSteps);
    }

    // Update tutorial with final metadata
    const updatedTutorial = await updateTutorial(currentTutorial.id, {
      steps: recordedSteps,
      updatedAt: new Date().toISOString()
    });

    // Reset recording state
    const completedTutorial = currentTutorial;
    recordingState = {
      isRecording: false,
      currentTutorialId: null,
      stepCount: 0,
      startTime: null
    };
    
    currentTutorial = null;

    // Update extension badge
    await chrome.action.setBadgeText({ text: '' });

    // Notify all tabs that recording stopped
    await notifyAllTabs('RECORDING_STOPPED', { tutorialId: completedTutorial.id });

    // Open tutorial in web app
    await chrome.tabs.create({
      url: `http://localhost:5173/tutorial/${completedTutorial.id}/edit`
    });

    // Save state
    await saveState();

    console.log('Recording stopped:', completedTutorial.id, 'Steps:', recordedSteps.length);
    
    return { success: true, tutorial: updatedTutorial };
  } catch (error) {
    console.error('Failed to stop recording:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Pause recording
 */
async function pauseRecording(): Promise<{ success: boolean }> {
  if (!recordingState.isRecording) {
    throw new Error('No recording in progress');
  }

  // Implementation for pause functionality
  await notifyAllTabs('RECORDING_PAUSED', {});
  await chrome.action.setBadgeText({ text: 'PAUSE' });
  await chrome.action.setBadgeBackgroundColor({ color: '#f59e0b' });

  return { success: true };
}

/**
 * Resume recording
 */
async function resumeRecording(): Promise<{ success: boolean }> {
  if (!recordingState.isRecording) {
    throw new Error('No recording in progress');
  }

  await notifyAllTabs('RECORDING_RESUMED', {});
  await chrome.action.setBadgeText({ text: 'REC' });
  await chrome.action.setBadgeBackgroundColor({ color: '#dc2626' });

  return { success: true };
}

/**
 * Get current recording status
 */
function getRecordingStatus(): RecordingState & { tutorial?: Tutorial; options: RecordingOptions } {
  return {
    ...recordingState,
    tutorial: currentTutorial || undefined,
    options: recordingOptions
  };
}

/**
 * Record a new step
 */
async function recordStep(
  stepData: Partial<RecordedStep>,
  tab?: chrome.tabs.Tab
): Promise<{ success: boolean; step?: RecordedStep; error?: string }> {
  try {
    if (!recordingState.isRecording || !currentTutorial) {
      throw new Error('No recording in progress');
    }

    const step: RecordedStep = {
      id: uuidv4(),
      order: recordedSteps.length + 1,
      title: stepData.title || generateStepTitle(stepData),
      description: stepData.description || generateStepDescription(stepData),
      action: stepData.action || 'click',
      element: stepData.element || '',
      elementText: stepData.elementText,
      inputValue: stepData.inputValue,
      url: tab?.url || stepData.url || '',
      screenshot: stepData.screenshot,
      annotations: stepData.annotations || [],
      metadata: stepData.metadata || {
        viewport: { width: 0, height: 0 },
        scrollPosition: { x: 0, y: 0 },
        pageTitle: tab?.title || '',
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      },
      timing: stepData.timing || 0,
      timestamp: new Date().toISOString()
    };

    recordedSteps.push(step);
    recordingState.stepCount = recordedSteps.length;

    // Take screenshot if enabled
    if (recordingOptions.captureScreenshots && tab?.id) {
      setTimeout(() => {
        takeScreenshot(tab.id!, { highlightElement: stepData.element as any });
      }, 500);
    }

    // Save state
    await saveState();

    console.log('Step recorded:', step.id, step.title);

    return { success: true, step };
  } catch (error) {
    console.error('Failed to record step:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Take screenshot of current tab
 */
async function takeScreenshot(
  tabId: number,
  options?: ScreenshotOptions
): Promise<{ success: boolean; imageUrl?: string; error?: string }> {
  try {
    // Capture visible tab
    const dataUrl = await chrome.tabs.captureVisibleTab(undefined, {
      format: options?.format || 'png',
      quality: options?.quality || recordingOptions.screenshotQuality
    });

    // Upload screenshot to backend
    const uploadResult = await uploadScreenshot(dataUrl);

    if (uploadResult.success && uploadResult.data) {
      // Update the last recorded step with screenshot URL
      if (recordedSteps.length > 0) {
        const lastStep = recordedSteps[recordedSteps.length - 1];
        lastStep.screenshot = uploadResult.data.imageUrl;
        await saveState();
      }

      return { success: true, imageUrl: uploadResult.data.imageUrl };
    } else {
      throw new Error(uploadResult.error || 'Failed to upload screenshot');
    }
  } catch (error) {
    console.error('Failed to take screenshot:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Update an existing step
 */
async function updateStep(
  stepId: string,
  updates: Partial<RecordedStep>
): Promise<{ success: boolean; step?: RecordedStep; error?: string }> {
  try {
    const stepIndex = recordedSteps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) {
      throw new Error('Step not found');
    }

    recordedSteps[stepIndex] = { ...recordedSteps[stepIndex], ...updates };
    await saveState();

    return { success: true, step: recordedSteps[stepIndex] };
  } catch (error) {
    console.error('Failed to update step:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Delete a step
 */
async function deleteStep(stepId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const stepIndex = recordedSteps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) {
      throw new Error('Step not found');
    }

    recordedSteps.splice(stepIndex, 1);

    // Reorder remaining steps
    recordedSteps.forEach((step, index) => {
      step.order = index + 1;
    });

    recordingState.stepCount = recordedSteps.length;
    await saveState();

    return { success: true };
  } catch (error) {
    console.error('Failed to delete step:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Generate step title based on action and element
 */
function generateStepTitle(stepData: Partial<RecordedStep>): string {
  const action = stepData.action || 'interact';
  const elementText = stepData.elementText || '';
  const element = stepData.element || '';

  switch (action) {
    case 'click':
      if (elementText) {
        return `Click "${elementText}"`;
      } else if (element.includes('button')) {
        return 'Click button';
      } else if (element.includes('link') || element.includes('a[')) {
        return 'Click link';
      } else {
        return 'Click element';
      }

    case 'type':
      if (element.includes('input[type="email"]') || element.includes('email')) {
        return 'Enter email address';
      } else if (element.includes('input[type="password"]') || element.includes('password')) {
        return 'Enter password';
      } else if (element.includes('textarea')) {
        return 'Enter text';
      } else {
        return 'Type in field';
      }

    case 'select':
      return elementText ? `Select "${elementText}"` : 'Select option';

    case 'navigate':
      return 'Navigate to page';

    case 'scroll':
      return 'Scroll page';

    case 'hover':
      return elementText ? `Hover over "${elementText}"` : 'Hover over element';

    default:
      return `${action.charAt(0).toUpperCase() + action.slice(1)} element`;
  }
}

/**
 * Generate detailed step description
 */
function generateStepDescription(stepData: Partial<RecordedStep>): string {
  const action = stepData.action || 'interact';
  const elementText = stepData.elementText || '';
  const element = stepData.element || '';
  const inputValue = stepData.inputValue || '';

  switch (action) {
    case 'click':
      if (elementText) {
        return `Click the "${elementText}" ${getElementType(element)}.`;
      } else {
        return `Click the ${getElementType(element)}.`;
      }

    case 'type':
      if (inputValue && !isSensitiveField(element)) {
        return `Enter "${inputValue}" in the ${getFieldName(element)} field.`;
      } else {
        return `Enter text in the ${getFieldName(element)} field.`;
      }

    case 'select':
      return `Select "${elementText}" from the dropdown menu.`;

    case 'navigate':
      return `Navigate to the page.`;

    case 'scroll':
      return 'Scroll down the page to view more content.';

    case 'hover':
      return `Hover over the ${elementText || 'element'} to reveal additional options.`;

    default:
      return `Perform ${action} action on the element.`;
  }
}

/**
 * Get element type from selector
 */
function getElementType(selector: string): string {
  if (selector.includes('button') || selector.includes('[type="button"]') || selector.includes('[type="submit"]')) {
    return 'button';
  } else if (selector.includes('a[') || selector.includes('link')) {
    return 'link';
  } else if (selector.includes('input')) {
    return 'input field';
  } else if (selector.includes('select')) {
    return 'dropdown';
  } else if (selector.includes('textarea')) {
    return 'text area';
  } else if (selector.includes('img')) {
    return 'image';
  } else {
    return 'element';
  }
}

/**
 * Get field name from selector
 */
function getFieldName(selector: string): string {
  if (selector.includes('email')) return 'email';
  if (selector.includes('password')) return 'password';
  if (selector.includes('name')) return 'name';
  if (selector.includes('phone')) return 'phone';
  if (selector.includes('address')) return 'address';
  if (selector.includes('search')) return 'search';
  return 'input';
}

/**
 * Check if field contains sensitive data
 */
function isSensitiveField(selector: string): boolean {
  const sensitiveFields = ['password', 'ssn', 'credit', 'card', 'cvv', 'security'];
  return sensitiveFields.some(field => selector.toLowerCase().includes(field));
}

/**
 * Notify all tabs about recording events
 */
async function notifyAllTabs(eventType: string, data: any): Promise<void> {
  try {
    const tabs = await chrome.tabs.query({});
    const message = {
      type: eventType,
      payload: data,
      timestamp: Date.now()
    };

    for (const tab of tabs) {
      if (tab.id) {
        chrome.tabs.sendMessage(tab.id, message).catch(() => {
          // Ignore errors for tabs without content script
        });
      }
    }
  } catch (error) {
    console.error('Failed to notify tabs:', error);
  }
}

// API Communication Functions

/**
 * Create a new tutorial via API
 */
async function createTutorial(title: string): Promise<Tutorial> {
  const response = await fetch(`${API_BASE_URL}/tutorials`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(authToken && { 'Authorization': `Bearer ${authToken}` })
    },
    body: JSON.stringify({
      title,
      description: `Tutorial recorded on ${new Date().toLocaleDateString()}`,
      isPublic: false
    })
  });

  const result: ApiResponse<Tutorial> = await response.json();

  if (!result.success || !result.data) {
    throw new Error(result.error || 'Failed to create tutorial');
  }

  return result.data;
}

/**
 * Update tutorial via API
 */
async function updateTutorial(tutorialId: string, updates: Partial<Tutorial>): Promise<Tutorial> {
  const response = await fetch(`${API_BASE_URL}/tutorials/${tutorialId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      ...(authToken && { 'Authorization': `Bearer ${authToken}` })
    },
    body: JSON.stringify(updates)
  });

  const result: ApiResponse<Tutorial> = await response.json();

  if (!result.success || !result.data) {
    throw new Error(result.error || 'Failed to update tutorial');
  }

  return result.data;
}

/**
 * Save tutorial steps via API
 */
async function saveTutorialSteps(tutorialId: string, steps: RecordedStep[]): Promise<void> {
  for (const step of steps) {
    try {
      await fetch(`${API_BASE_URL}/steps/tutorial/${tutorialId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        },
        body: JSON.stringify({
          title: step.title,
          description: step.description,
          action: step.action,
          element: step.element,
          elementText: step.elementText,
          inputValue: step.inputValue,
          url: step.url,
          screenshot: step.screenshot,
          annotations: step.annotations,
          metadata: step.metadata,
          timing: step.timing
        })
      });
    } catch (error) {
      console.error('Failed to save step:', step.id, error);
    }
  }
}

/**
 * Upload screenshot to backend
 */
async function uploadScreenshot(dataUrl: string): Promise<ApiResponse<UploadResponse>> {
  try {
    // Convert data URL to blob
    const response = await fetch(dataUrl);
    const blob = await response.blob();

    // Create form data
    const formData = new FormData();
    formData.append('screenshot', blob, `screenshot-${Date.now()}.png`);

    // Upload to backend
    const uploadResponse = await fetch(`${API_BASE_URL}/upload/screenshot`, {
      method: 'POST',
      headers: {
        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
      },
      body: formData
    });

    return await uploadResponse.json();
  } catch (error) {
    console.error('Failed to upload screenshot:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Initialize analytics tracking
 */
async function initializeAnalytics(): Promise<void> {
  try {
    // Track extension installation/startup
    await fetch(`${API_BASE_URL}/analytics/track`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
      },
      body: JSON.stringify({
        event: 'extension_started',
        properties: {
          version: chrome.runtime.getManifest().version,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        }
      })
    }).catch(() => {
      // Ignore analytics errors
    });
  } catch (error) {
    console.error('Failed to initialize analytics:', error);
  }
}

// Tab event listeners for navigation tracking
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (recordingState.isRecording && changeInfo.status === 'complete' && tab.url) {
    // Record navigation step
    await recordStep({
      action: 'navigate',
      url: tab.url,
      title: `Navigate to ${tab.title || tab.url}`,
      description: `Navigate to ${tab.url}`,
      metadata: {
        viewport: { width: 0, height: 0 },
        scrollPosition: { x: 0, y: 0 },
        pageTitle: tab.title || '',
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      }
    }, tab);
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener(async (tab) => {
  // Open popup or toggle recording
  if (recordingState.isRecording) {
    await stopRecording();
  } else {
    // Open popup for recording options
    chrome.action.openPopup();
  }
});

console.log('Scribe Pro background service worker loaded');
