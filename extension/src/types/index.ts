// Types for Scribe Pro Chrome Extension

export interface RecordingState {
  isRecording: boolean;
  currentTutorialId: string | null;
  stepCount: number;
  startTime: number | null;
}

export interface RecordedStep {
  id: string;
  order: number;
  title: string;
  description: string;
  action: ActionType;
  element: string;
  elementText?: string;
  inputValue?: string;
  url: string;
  screenshot?: string;
  annotations: Annotation[];
  metadata: StepMetadata;
  timing: number;
  timestamp: string;
}

export type ActionType = 
  | 'click'
  | 'type'
  | 'navigate'
  | 'scroll'
  | 'hover'
  | 'select'
  | 'submit'
  | 'wait'
  | 'custom';

export interface Annotation {
  id: string;
  type: 'arrow' | 'highlight' | 'blur' | 'text' | 'circle' | 'rectangle';
  x: number;
  y: number;
  width?: number;
  height?: number;
  text?: string;
  color?: string;
  style?: Record<string, any>;
}

export interface StepMetadata {
  viewport: {
    width: number;
    height: number;
  };
  scrollPosition: {
    x: number;
    y: number;
  };
  elementBounds?: DOMRect;
  pageTitle: string;
  userAgent: string;
  timestamp: number;
}

export interface InteractionEvent {
  type: 'click' | 'input' | 'change' | 'scroll' | 'hover' | 'navigate';
  target: Element;
  coordinates?: { x: number; y: number };
  value?: string;
  timestamp: number;
}

export interface ElementInfo {
  selector: string;
  text: string;
  tagName: string;
  type?: string;
  placeholder?: string;
  ariaLabel?: string;
  title?: string;
  id?: string;
  className?: string;
  bounds: DOMRect;
}

export interface ScreenshotOptions {
  format: 'png' | 'jpeg';
  quality?: number;
  highlightElement?: Element;
  annotations?: Annotation[];
}

export interface Tutorial {
  id: string;
  title: string;
  description?: string;
  steps: RecordedStep[];
  createdAt: string;
  updatedAt: string;
}

// Message types for communication between extension parts
export interface Message {
  type: MessageType;
  payload?: any;
  timestamp: number;
}

export type MessageType =
  | 'START_RECORDING'
  | 'STOP_RECORDING'
  | 'PAUSE_RECORDING'
  | 'RESUME_RECORDING'
  | 'GET_RECORDING_STATUS'
  | 'RECORD_STEP'
  | 'TAKE_SCREENSHOT'
  | 'UPDATE_STEP'
  | 'DELETE_STEP'
  | 'RECORDING_STARTED'
  | 'RECORDING_STOPPED'
  | 'RECORDING_PAUSED'
  | 'RECORDING_RESUMED'
  | 'STEP_RECORDED'
  | 'SCREENSHOT_TAKEN'
  | 'ERROR';

export interface StartRecordingMessage extends Message {
  type: 'START_RECORDING';
  payload: {
    tutorialTitle: string;
    options?: RecordingOptions;
  };
}

export interface StopRecordingMessage extends Message {
  type: 'STOP_RECORDING';
}

export interface RecordStepMessage extends Message {
  type: 'RECORD_STEP';
  payload: {
    step: Partial<RecordedStep>;
  };
}

export interface TakeScreenshotMessage extends Message {
  type: 'TAKE_SCREENSHOT';
  payload?: {
    stepIndex?: number;
    options?: ScreenshotOptions;
  };
}

export interface RecordingOptions {
  captureScreenshots: boolean;
  captureScrolling: boolean;
  captureHovers: boolean;
  captureFormInputs: boolean;
  captureNavigation: boolean;
  autoGenerateDescriptions: boolean;
  highlightElements: boolean;
  blurSensitiveData: boolean;
  maxSteps?: number;
  screenshotQuality: number;
}

export interface RecordingSettings {
  apiEndpoint: string;
  authToken?: string;
  autoUpload: boolean;
  compressionLevel: number;
  maxFileSize: number;
  allowedDomains: string[];
  excludedSelectors: string[];
  sensitiveDataSelectors: string[];
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface UploadResponse {
  imageUrl: string;
  width: number;
  height: number;
  size: number;
  format: string;
}

// Storage types
export interface ExtensionStorage {
  recordingState: RecordingState;
  settings: RecordingSettings;
  tutorials: Tutorial[];
  currentTutorial?: Tutorial;
  recentSteps: RecordedStep[];
}

// DOM utilities
export interface DOMUtils {
  getElementSelector(element: Element): string;
  getElementInfo(element: Element): ElementInfo;
  highlightElement(element: Element, options?: HighlightOptions): void;
  removeHighlight(element: Element): void;
  isElementVisible(element: Element): boolean;
  isElementInteractive(element: Element): boolean;
  getElementText(element: Element): string;
  captureElementScreenshot(element: Element): Promise<string>;
}

export interface HighlightOptions {
  color?: string;
  style?: 'outline' | 'background' | 'border';
  duration?: number;
  animation?: boolean;
}

// Event handlers
export interface EventHandlers {
  onRecordingStart: (tutorial: Tutorial) => void;
  onRecordingStop: (tutorial: Tutorial) => void;
  onStepRecorded: (step: RecordedStep) => void;
  onScreenshotTaken: (imageUrl: string) => void;
  onError: (error: Error) => void;
}

// Chrome extension specific types
export interface ChromeTab {
  id?: number;
  url?: string;
  title?: string;
  active?: boolean;
  windowId?: number;
}

export interface ChromeMessage {
  type: string;
  payload?: any;
  tabId?: number;
  frameId?: number;
}

// Analytics types
export interface AnalyticsEvent {
  event: string;
  properties: Record<string, any>;
  timestamp: number;
  sessionId: string;
  userId?: string;
}

export interface RecordingAnalytics {
  sessionId: string;
  startTime: number;
  endTime?: number;
  totalSteps: number;
  totalDuration: number;
  screenshotCount: number;
  errorCount: number;
  userAgent: string;
  viewport: { width: number; height: number };
  domains: string[];
}

// Error types
export class ExtensionError extends Error {
  code: string;
  details?: any;

  constructor(message: string, code: string, details?: any) {
    super(message);
    this.name = 'ExtensionError';
    this.code = code;
    this.details = details;
  }
}

export class RecordingError extends ExtensionError {
  constructor(message: string, details?: any) {
    super(message, 'RECORDING_ERROR', details);
  }
}

export class ScreenshotError extends ExtensionError {
  constructor(message: string, details?: any) {
    super(message, 'SCREENSHOT_ERROR', details);
  }
}

export class APIError extends ExtensionError {
  constructor(message: string, details?: any) {
    super(message, 'API_ERROR', details);
  }
}
