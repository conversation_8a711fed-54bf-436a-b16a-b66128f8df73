# Scribe Pro - Advanced Tutorial Recording Platform

A comprehensive fullstack web application that intelligently records user browser interactions, automatically captures contextual screenshots, and generates polished, editable step-by-step tutorials. Built with modern TypeScript architecture and production-ready features.

## 🚀 Features

### 🎥 **Intelligent Chrome Extension (Manifest V3)**
- Records ALL user interactions: clicks, form inputs, dropdown selections, page navigation, scrolling
- Captures high-quality screenshots automatically after each meaningful action
- Extracts intelligent contextual information: button text, input placeholders, link text, form labels
- Detects and highlights specific UI elements being interacted with
- Generates human-readable action descriptions
- Real-time recording indicator and step counter
- Handles SPAs and dynamic content changes

### 🧠 **Smart Tutorial Generation Engine**
- Automatically creates numbered step sequences with screenshots and descriptions
- Generates contextually appropriate action verbs (click, type, select, navigate, etc.)
- Detects form submissions, page transitions, and modal interactions
- Creates meaningful step titles and detailed descriptions
- Support for conditional steps and branching workflows

### 🎨 **Advanced Web Application (React + TypeScript)**
- Modern, responsive dashboard for managing tutorials
- Advanced step editor with drag-and-drop reordering
- Rich text editing for descriptions
- Image annotation tools (arrows, highlights, blur sensitive data)
- Step merging/splitting capabilities
- Real-time preview of tutorials
- Collaborative editing features
- Version history and rollback functionality

### 📤 **Professional Export & Sharing**
- Professional PDF export with branded templates
- Responsive HTML export that works on all devices
- Embeddable widget for websites (iframe)
- Public shareable links with view analytics
- Integration with popular platforms (Notion, Confluence, Zendesk)
- Bulk export options for multiple tutorials

## 🏗️ Architecture

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript + PostgreSQL
- **Extension**: Chrome Extension (Manifest V3) with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Storage**: AWS S3 compatible storage for images
- **Cache**: Redis for session management and caching
- **Auth**: JWT-based authentication with refresh tokens

## Quick Start

1. **Install dependencies**:
   ```bash
   npm run install:all
   ```

2. **Start development servers**:
   ```bash
   npm run dev
   ```

3. **Load Chrome Extension**:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `extension` folder

## Project Structure

```
├── backend/          # Node.js API server
├── frontend/         # React web application
├── extension/        # Chrome extension
└── package.json      # Root package configuration
```

## Development

- Backend runs on `http://localhost:3001`
- Frontend runs on `http://localhost:5173`
- Chrome extension loads from `./extension` folder

## Usage

1. Install and activate the Chrome extension
2. Navigate to any webpage
3. Click the extension icon and start recording
4. Perform actions on the webpage
5. Stop recording and view/edit the tutorial in the web app
6. Export as PDF or HTML

## MVP Features

- ✅ Record user interactions
- ✅ Capture screenshots
- ✅ Display recorded steps
- ✅ Edit tutorial content
- ✅ Export to PDF/HTML
